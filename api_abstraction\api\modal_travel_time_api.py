from functools import cache
from typing import Dict, List, Optional, Tuple

from api_abstraction.api.travel_time_api import ApiF<PERSON>, JourneyAttribute, TravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode


class ModalTravelTimeApi(TravelTimeApi):
    def __init__(
        self,
        mode_apis: Dict[TransportMode, TravelTimeApi],
        isochrone_api: TravelTimeApi,
    ) -> None:
        self.mode_apis = mode_apis
        self.isochrone_api = isochrone_api

    @cache
    def time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        if mode in self.mode_apis:
            return self.mode_apis[mode].time(
                origin, destination, mode, arrival_time, max_transfers
            )
        else:
            raise ApiFail(f"Mode {mode.value} not available")

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        return self.isochrone_api.compute_isochrone(
            territory, destination, transport_mode, boundary
        )

    def compute_detour_costs(
        self,
        origins: List[GeoCoordinates],
        destination: GeoCoordinates,
    ) -> Dict[int, Dict[int, float]]:
        if TransportMode.CAR in self.mode_apis:
            return self.mode_apis[TransportMode.CAR].compute_detour_costs(
                origins, destination
            )
        else:
            raise ApiFail(f"Mode {TransportMode.CAR} not available")

    def compute_detours(
        self,
        origins: List[GeoCoordinates],
        destination: GeoCoordinates,
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        if TransportMode.CAR in self.mode_apis:
            return self.mode_apis[TransportMode.CAR].compute_detours(
                origins, destination
            )
        else:
            raise ApiFail(f"Mode {TransportMode.CAR} not available")
