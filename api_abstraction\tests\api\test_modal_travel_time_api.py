import pytest

from api_abstraction.api.modal_travel_time_api import ModalTravelTimeApi
from api_abstraction.api.travel_time_api import ApiFail
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode


class TestModalTravelTimeApi:
    def test_api_calls_walk_sub_api(self, fake_travel_timer) -> None:
        modal_api = ModalTravelTimeApi(
            {
                TransportMode.WALK: fake_travel_timer(22),
                TransportMode.PUBLIC_TRANSPORT: fake_travel_timer(55),
            },
            fake_travel_timer(22),
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 2.0)

        attributes = modal_api.time(origin, destination, TransportMode.WALK, (8, 30))

        assert attributes.duration == 22

    def test_api_calls_pt_sub_api(self, fake_travel_timer) -> None:
        modal_api = ModalTravelTimeApi(
            {
                TransportMode.WALK: fake_travel_timer(22),
                TransportMode.PUBLIC_TRANSPORT: fake_travel_timer(55),
            },
            fake_travel_timer(22),
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 2.0)

        attributes = modal_api.time(
            origin, destination, TransportMode.PUBLIC_TRANSPORT, (8, 30)
        )

        assert attributes.duration == 55

    def test_api_fails_if_mode_unavailable(self, fake_travel_timer) -> None:
        modal_api = ModalTravelTimeApi(
            {TransportMode.WALK: fake_travel_timer(22)}, fake_travel_timer(22)
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 2.0)

        with pytest.raises(ApiFail) as e:
            modal_api.time(origin, destination, TransportMode.PUBLIC_TRANSPORT, (8, 30))
        assert e.value.message == "Mode PUBLIC_TRANSPORT not available"

    def test_compute_isochrone(self, fake_travel_timer) -> None:
        modal_api = ModalTravelTimeApi(
            {TransportMode.WALK: fake_travel_timer(22)}, fake_travel_timer(50)
        )

        isochrone = modal_api.compute_isochrone(
            Territory.LYON,
            GeoCoordinates(45.0, 5.0),
            TransportMode.PUBLIC_TRANSPORT,
            3600,
        )

        assert "coordinates" in isochrone
        assert "type" in isochrone
        assert isinstance(isochrone["coordinates"], list)
