from typing import List, Union

from mobility.ir.cost import Cost, CostKind, CostPayer
from mobility.ir.site import CoworkingSite, GeoSite
from mobility.quantity import (
    Quantity,
    euros,
    euros_2020,
    meters,
    trip,
    yearly,
)
from mobility.workers.rent_price_computer import get_office_rent_price

CAR_PARKING_COST = 745 * euros_2020 / yearly
BICYCLE_PARKING_YEARLY_COST = 124 * euros_2020 / yearly
COWORKING_YEARLY_RENT_COST = 500 * euros * 12 / yearly
SURFACE_REQUIRED_BY_EMPLOYEE = 18 * meters * meters


class InfrastructureCostCalculator:
    def compute_car_parking_infrastructure_cost(self) -> Quantity:
        return CAR_PARKING_COST

    def compute_bicycle_parking_infrastructure_cost(self) -> Quantity:
        return BICYCLE_PARKING_YEARLY_COST

    def _compute_car_parking_commute_costs(
        self,
        site: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        if isinstance(site, CoworkingSite):
            return []

        company_base_infrastructure_cost = (
            self.compute_car_parking_infrastructure_cost()
        )
        costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=company_base_infrastructure_cost,
            )
        ]
        if isinstance(site, GeoSite) and site.car_parking_cost is not None:
            employee_fee = site.car_parking_cost * euros / trip
            costs += [
                Cost(
                    kind=CostKind.CAR_PARKING,
                    payer=CostPayer.EMPLOYEE,
                    amount=employee_fee,
                ),
                Cost(
                    kind=CostKind.CAR_PARKING,
                    payer=CostPayer.COMPANY,
                    amount=-employee_fee,
                ),
            ]

        return costs

    def _compute_bicycle_parking_commute_costs(
        self,
        site: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        if isinstance(site, CoworkingSite):
            return []

        company_base_infrastructure_cost = (
            self.compute_bicycle_parking_infrastructure_cost()
        )
        costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=company_base_infrastructure_cost,
            )
        ]
        if isinstance(site, GeoSite) and site.bicycle_parking_cost is not None:
            employee_fee = site.bicycle_parking_cost * euros / trip
            costs += [
                Cost(
                    kind=CostKind.BIKE_PARKING,
                    payer=CostPayer.EMPLOYEE,
                    amount=employee_fee,
                ),
                Cost(
                    kind=CostKind.BIKE_PARKING,
                    payer=CostPayer.COMPANY,
                    amount=-employee_fee,
                ),
            ]

        return costs

    def compute_office_rent_cost(self, site: GeoSite) -> Quantity:
        office_rent_price = get_office_rent_price(site)
        return SURFACE_REQUIRED_BY_EMPLOYEE * office_rent_price * 1.4

    def compute_coworking_rent_cost(self, site: CoworkingSite) -> Quantity:
        return COWORKING_YEARLY_RENT_COST
