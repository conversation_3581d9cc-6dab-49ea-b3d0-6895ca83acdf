from typing import Any
from unittest import mock

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.manual.manual_geocoder import ManualGeocoder


class TestManualGeocoder:
    @mock.patch("api_abstraction.manual.manual_geocoder.input")
    def test_should_geocode_from_input(
        self, fake_input: Any, geo_coordinates: Any
    ) -> None:
        fake_input.return_value = "84.0 154.2"
        geocoder = ManualGeocoder("", EventReporter())

        coords = geocoder.geocode("An address")

        assert coords == geo_coordinates(latitude=84.0, longitude=154.2)

    @mock.patch("api_abstraction.manual.manual_geocoder.input")
    def test_should_retry_if_input_is_wrong(
        self, fake_input: Any, geo_coordinates: Any
    ) -> None:
        fake_input.side_effect = ["wat", "84.0 154.2"]
        geocoder = ManualGeocoder("", EventReporter())

        coords = geocoder.geocode("An address")

        assert coords == geo_coordinates(latitude=84.0, longitude=154.2)

    @mock.patch("api_abstraction.manual.manual_geocoder.input")
    def test_should_geocode_reverse_from_input(
        self, fake_input: Any, geo_coordinates: Any, address: Any
    ) -> None:
        fake_input.side_effect = [
            "the full address",
            "Lyon",
            "whatever",
            "459813543",
        ]
        geocoder = ManualGeocoder("", EventReporter())

        coords = geocoder.reverse(geo_coordinates(45.5, 5.0))

        assert coords == address(
            full="the full address",
            normalized="the full address",
            city="Lyon",
            postcode="whatever",
            citycode="459813543",
            coordinates=geo_coordinates(45.5, 5.0),
        )

    @mock.patch("api_abstraction.manual.manual_geocoder.input")
    def test_should_geocode_details_from_input(
        self, fake_input: Any, geo_coordinates: Any, address: Any
    ) -> None:
        fake_input.side_effect = [
            "the normalized address",
            "(45.5, 5.0)",
            "Lyon",
            "whatever",
            "459813543",
        ]
        geocoder = ManualGeocoder("", EventReporter())

        coords = geocoder.geocode_details("An address")

        assert coords == address(
            full="An address",
            normalized="the normalized address",
            city="Lyon",
            postcode="whatever",
            citycode="459813543",
            coordinates=geo_coordinates(45.5, 5.0),
        )

    @mock.patch("api_abstraction.manual.manual_geocoder.input")
    def test_should_geocode_details_from_input_and_retry_until_it_gets_coordinates(
        self, fake_input: Any, geo_coordinates: Any, address: Any
    ) -> None:
        fake_input.side_effect = [
            "the normalized address",
            "not coords...",
            "still not coords...",
            "(45.5, 5.0)",
            "Lyon",
            "whatever",
            "459813543",
        ]
        geocoder = ManualGeocoder("", EventReporter())

        coords = geocoder.geocode_details("An address")

        assert coords == address(
            full="An address",
            normalized="the normalized address",
            city="Lyon",
            postcode="whatever",
            citycode="459813543",
            coordinates=geo_coordinates(45.5, 5.0),
        )
