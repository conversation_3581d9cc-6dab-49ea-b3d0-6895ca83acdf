import logging
from collections import defaultdict
from datetime import datetime, time, timedelta
from typing import Dict, Optional, Tuple

from geopy.distance import distance as compute_crow_fly_distance

from api_abstraction.api.api import ApiInapt
from api_abstraction.api.date_iterators import DateTimeInterval
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.hypotheses import closest_tuesday_8_30_in_interval
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from api_abstraction.google.base_google_api import (
    GoogleClientAPI,
    GoogleRequestParameters,
)
from api_abstraction.google.distance_matrix import DistanceMatrix
from api_abstraction.google.routes_api import RoutesAPI
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import Quantity, hours, kilometers, meters, minutes, seconds


class GoogleTravelTimeAPI(TravelTimeApi):
    def __init__(
        self,
        token: str,
        reporter: EventReporter,
        use_routes_api: bool = False,
        json_key_path: Optional[str] = None,
    ) -> None:
        if use_routes_api and json_key_path:
            self.google_routing_client: GoogleClientAPI = RoutesAPI(json_key_path)
            client_name = "RoutesAPI"
        else:
            self.google_routing_client = DistanceMatrix(token)
            client_name = "DistanceMatrix"
        self.ok_status = "OK"
        self.car_journeys: Dict[
            GeoCoordinates, Dict[GeoCoordinates, Dict[Quantity, JourneyAttribute]]
        ] = defaultdict(lambda: defaultdict(lambda: defaultdict(None)))
        self.nb_queries_done: Dict[
            GeoCoordinates, Dict[GeoCoordinates, Dict[Tuple[int, int], int]]
        ] = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        super().__init__(token, reporter)
        self.request_date = self._next_tuesday_8_30()
        self.logger = logging.getLogger("api")
        self.logger.info(
            f"Initialized {client_name} with request_date={self.request_date}"
        )
        self.traffic_model = "pessimistic"
        self.car_like_modes = [
            TransportMode.CAR,
            TransportMode.ELECTRIC_CAR,
            TransportMode.CARPOOLING,
            TransportMode.MOTORCYCLE,
        ]
        self.unhandled_modes = [TransportMode.ELECTRIC_BICYCLE]

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        self.logger.info(
            f"GoogleTravelTimeAPI Timing ({origin})-[{mode.value}]->({destination})"
        )
        if mode in self.unhandled_modes:
            raise ApiInapt(f"{mode} not handled")
        tolerance = 10 * minutes
        compute_route_result = self._compute_lin_reg_result_based_on_store(
            origin, destination, mode, arrival_time, tolerance
        )
        if compute_route_result is None:
            compute_route_result = self._compute_for_arrival_time(
                origin, destination, mode, arrival_time, tolerance
            )
        return compute_route_result

    def _get_stored_result_closest_to_arrival(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        tolerance: Optional[Quantity],
    ) -> Optional[JourneyAttribute]:
        if mode in self.car_like_modes:
            hour, minute = arrival_time
            requested_time = hour * hours + minute * minutes
            closest_journey = None
            if (
                destination in self.car_journeys
                and origin in self.car_journeys[destination]
            ):
                self.logger.info("    OD is stored in car_journeys")
                closest_delta_arrival = None
                for arrival_qty, journey in self.car_journeys[destination][
                    origin
                ].items():
                    if (
                        tolerance is None
                        or requested_time - tolerance
                        < arrival_qty
                        < requested_time + tolerance
                    ):
                        delta_arrival = abs(arrival_qty - requested_time)
                        if (
                            closest_delta_arrival is None
                            or delta_arrival < closest_delta_arrival
                        ):
                            closest_delta_arrival = delta_arrival
                            closest_journey = journey
                self.logger.info(f"    Closest journey found :\n{closest_journey}")
            else:
                self.logger.info("    OD is not stored in car_journeys")
            return closest_journey
        else:
            return None

    def _compute_lin_reg_result_based_on_store(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        tolerance: Optional[Quantity],
    ) -> Optional[JourneyAttribute]:
        if mode in self.car_like_modes:
            hour, minute = arrival_time
            requested_time = hour * hours + minute * minutes
            inf_data, sup_data = self._compute_inf_sup_journeys(
                origin, destination, requested_time
            )
            if inf_data is None or sup_data is None:
                self.logger.info(
                    "    Could not find inf and sup data in stored journeys\n"
                    f"    INF={inf_data} SUP={sup_data}"
                )
                return None
            else:
                inf_arrival, inf_journey = inf_data
                sup_arrival, sup_journey = sup_data
                inf_departure = inf_arrival - inf_journey.duration * seconds
                sup_departure = sup_arrival - sup_journey.duration * seconds
                delta_departure_time = sup_departure - inf_departure
                if tolerance is None or delta_departure_time < tolerance:
                    rate = (requested_time - inf_arrival) / (sup_arrival - inf_arrival)
                    projected_duration = inf_journey.duration + int(
                        rate * (sup_journey.duration - inf_journey.duration)
                    )
                    if sup_journey.distance is None or inf_journey.distance is None:
                        projected_distance = None
                    else:
                        projected_distance = inf_journey.distance + int(
                            rate * (sup_journey.distance - inf_journey.distance)
                        )
                    computed_journey = JourneyAttribute(
                        duration=projected_duration,
                        distance=projected_distance,
                        emission=None,
                    )
                    self.logger.info(
                        f"    Computed journey {computed_journey} based on:\n"
                        f"        inf: {inf_departure} -> {inf_arrival} ({inf_journey})\n"
                        f"        sup: {sup_departure} -> {sup_arrival} ({sup_journey})"
                    )
                    return computed_journey
                else:
                    self.logger.info(
                        "    Inf/sup journeys not within tolerance"
                        f" {delta_departure_time} >= {tolerance}"
                    )
                    return None
        else:
            return None

    def _compute_for_arrival_time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        tolerance: Quantity,
    ) -> JourneyAttribute:
        hour, minute = arrival_time
        if mode in self.car_like_modes:
            n_max_tries = 4 - self._get_number_of_tries_performed(
                origin, destination, arrival_time
            )
            for n_try in range(n_max_tries):
                expected_duration = self._compute_expected_car_duration(
                    origin, destination, arrival_time
                )
                departure_time = hour * hours + minute * minutes - expected_duration
                request_departure_time = self.request_date.replace(
                    hour=0, minute=0
                ) + timedelta(
                    hours=int(departure_time // hours),
                    minutes=int(departure_time % hours // minutes),
                )
                parameters = GoogleRequestParameters(
                    origin=origin,
                    destination=destination,
                    mode=mode,
                    departure_time=request_departure_time,
                    traffic_model=self.traffic_model,
                )
                journey = self.google_routing_client.compute_route(parameters)
                actual_arrival_time = departure_time + journey.duration * seconds
                self.logger.info(
                    f"    API result for departure at {departure_time}:\n"
                    f"        {journey}\n"
                    f"        Arrival at {actual_arrival_time}"
                )
                self._record_car_journey(
                    origin, destination, actual_arrival_time, journey
                )
                self._record_try_performed(origin, destination, arrival_time)
                computed_res = self._compute_lin_reg_result_based_on_store(
                    origin, destination, mode, arrival_time, tolerance
                )
                if computed_res is not None:
                    return computed_res
            forced_res = self._compute_lin_reg_result_based_on_store(
                origin, destination, mode, arrival_time, None
            )
            if forced_res is None:
                forced_res = self._get_stored_result_closest_to_arrival(
                    origin,
                    destination,
                    mode,
                    arrival_time,
                    None,
                )
                self.logger.warning(f"    Picking result in store:\n{forced_res}")
            if forced_res is None:
                raise ValueError("No result stored after computation")
            return forced_res
        else:
            request_arrival_time = self.request_date.replace(hour=hour, minute=minute)
            parameters = GoogleRequestParameters(
                origin=origin,
                destination=destination,
                mode=mode,
                arrival_time=request_arrival_time,
                traffic_model=self.traffic_model,
            )
            journey = self.google_routing_client.compute_route(parameters)
            self.logger.info(f"    API result {journey}")
            return journey

    def _compute_expected_car_duration(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        arrival_time: Tuple[int, int],
    ) -> Quantity:
        hour, minute = arrival_time
        requested_time = hour * hours + minute * minutes
        inf_journey, sup_journey = self._compute_inf_sup_journeys(
            origin, destination, requested_time
        )
        if inf_journey is None:
            if sup_journey is None:
                od_distance = (
                    int(
                        compute_crow_fly_distance(
                            (origin.latitude, origin.longitude),
                            (destination.latitude, destination.longitude),
                        ).m
                    )
                    * meters
                )
                median_speed = 10 * kilometers / hours
                return od_distance / median_speed
            else:
                return sup_journey[1].duration * seconds
        else:
            if sup_journey is None:
                return inf_journey[1].duration * seconds
            else:
                time_delta = float((sup_journey[0] - inf_journey[0]) / seconds)
                inf_coeff = float((sup_journey[0] - requested_time) / seconds)
                sup_coeff = float((requested_time - inf_journey[0]) / seconds)
                inf_duration = float(inf_journey[1].duration)
                sup_duration = float(sup_journey[1].duration)
                expected_duration = seconds * int(
                    (inf_coeff * inf_duration + sup_coeff * sup_duration) / time_delta
                )
                self.logger.debug(f"    - {inf_journey} -> {sup_journey}")
                self.logger.debug(f"    expected_duration {expected_duration}")
                return expected_duration

    def _compute_inf_sup_journeys(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        requested_time: Quantity,
    ) -> Tuple[
        Optional[Tuple[Quantity, JourneyAttribute]],
        Optional[Tuple[Quantity, JourneyAttribute]],
    ]:
        journeys_with_departure = []
        if destination in self.car_journeys:
            if origin in self.car_journeys[destination]:
                for arrival_qty, journey in self.car_journeys[destination][
                    origin
                ].items():
                    departure_qty = arrival_qty - journey.duration * seconds
                    with_departure = (departure_qty, arrival_qty, journey)
                    journeys_with_departure.append(with_departure)
        self.logger.debug(f"Recorded journey: {journeys_with_departure}")
        if len(journeys_with_departure) == 0:
            return None, None
        journeys_ordered_by_departure = sorted(
            journeys_with_departure, key=lambda triplet: triplet[0]
        )
        if all(arrival >= requested_time for _, arrival, _ in journeys_with_departure):
            _, first_arrival, first_journey = journeys_ordered_by_departure[0]
            sup = first_arrival, first_journey
            return None, sup
        if all(arrival < requested_time for _, arrival, _ in journeys_with_departure):
            _, last_arrival, last_journey = journeys_ordered_by_departure[-1]
            inf = last_arrival, last_journey
            return inf, None
        for first_to_leave, last_to_leave in zip(
            journeys_ordered_by_departure[:-1], journeys_ordered_by_departure[1:]
        ):
            first_departure, first_to_leave_arrival, first_to_leave_journey = (
                first_to_leave
            )
            last_departure, last_to_leave_arrival, last_to_leave_journey = last_to_leave
            if (
                first_to_leave_arrival < requested_time
                and last_to_leave_arrival >= requested_time
            ):
                inf = first_to_leave_arrival, first_to_leave_journey
                sup = last_to_leave_arrival, last_to_leave_journey
            elif (
                last_to_leave_arrival < requested_time
                and first_to_leave_arrival >= requested_time
            ):
                inf = last_to_leave_arrival, last_to_leave_journey
                sup = first_to_leave_arrival, first_to_leave_journey
        return inf, sup

    def _record_car_journey(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        arrival_time: Quantity,
        journey: JourneyAttribute,
    ) -> None:
        self.car_journeys[destination][origin][arrival_time] = journey

    def _record_try_performed(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        requested_arrival_time: Tuple[int, int],
    ) -> None:
        self.nb_queries_done[destination][origin][requested_arrival_time] += 1

    def _get_number_of_tries_performed(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        requested_arrival_time: Tuple[int, int],
    ) -> int:
        return self.nb_queries_done[destination][origin][requested_arrival_time]

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        raise TypeError("Google Api has no isochrone computing capability")

    def _next_tuesday_8_30(self) -> datetime:
        start = datetime.combine(self._get_today(), time(hour=8, minute=30))
        return closest_tuesday_8_30_in_interval(
            DateTimeInterval(start + timedelta(days=7), None), self._get_today()
        )
