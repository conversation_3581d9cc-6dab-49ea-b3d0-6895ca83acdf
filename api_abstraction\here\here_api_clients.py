from datetime import datetime
from typing import Dict, List, Optional

import flexpolyline
from shapely.geometry import shape
from shapely.geometry.base import BaseGeometry

from api_abstraction.api.api import ApiFail
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.here.base_here_api import BaseHereAPI
from mobility.ir.here import (
    HereIntermodalRequestParameters,
    HereIsolineRequestParameters,
    HerePublicTransitRequestParameters,
    HereRoutingRequestParameters,
)


class HereRoutingAPI(BaseHereAPI[HereRoutingRequestParameters, JourneyAttribute]):
    display_name = "HERE Routing API"
    root_url = "https://router.hereapi.com/v8/routes"

    def compute_route(
        self, parameters: HereRoutingRequestParameters
    ) -> JourneyAttribute:
        params = self._format_input_parameters(parameters)
        result = self._make_request(self.root_url, params)
        return self._format_result(result)

    def _format_input_parameters(
        self, parameters: HereRoutingRequestParameters
    ) -> Dict:
        if parameters.mode is None:
            raise ApiFail("mode is required for route computation.")
        origin_str = f"{parameters.origin.latitude},{parameters.origin.longitude}"
        dest_str = (
            f"{parameters.destination.latitude},{parameters.destination.longitude}"
        )
        params = {
            "transportMode": self._map_here_mode(parameters.mode),
            "origin": origin_str,
            "destination": dest_str,
            "return": ",".join(parameters.return_values),
            "routingMode": parameters.routing_mode.value,
            "arrivalTime": parameters.arrival_time.isoformat(),
        }

        return params

    def _format_result(self, result: Dict) -> JourneyAttribute:
        try:
            return convert_here_result_to_journey_attribute(result)
        except (KeyError, IndexError, ApiFail) as e:
            raise ApiFail(f"Failed to parse {self.display_name} response: {str(e)}")


class HerePublicTransitAPI(
    BaseHereAPI[HerePublicTransitRequestParameters, JourneyAttribute]
):
    display_name = "HERE Transit API"
    root_url = "https://transit.router.hereapi.com/v8/routes"

    def compute_route(
        self, parameters: HerePublicTransitRequestParameters
    ) -> JourneyAttribute:
        params = self._format_input_parameters(parameters)
        result = self._make_request(self.root_url, params)
        return self._format_result(result)

    def _format_input_parameters(
        self, parameters: HerePublicTransitRequestParameters
    ) -> Dict:
        origin_str = f"{parameters.origin.latitude},{parameters.origin.longitude}"
        dest_str = (
            f"{parameters.destination.latitude},{parameters.destination.longitude}"
        )
        check_max_transfers_is_in_range(parameters.max_transfers)

        params = {
            "origin": origin_str,
            "destination": dest_str,
            "return": ",".join(parameters.return_values),
            "arrivalTime": parameters.arrival_time.isoformat(),
            "changes": parameters.max_transfers,
            "pedestrian[speed]": parameters.walk_speed,
        }
        transit_modes = []
        if len(parameters.include_transit_modes) > 0:
            transit_modes.extend(
                [mode.value for mode in parameters.include_transit_modes]
            )
        if len(parameters.exclude_transit_modes) > 0:
            transit_modes.extend(
                [f"-{mode.value}" for mode in parameters.exclude_transit_modes]
            )
        if transit_modes:
            params["transit[modes]"] = ",".join(transit_modes)

        return params

    def _format_result(self, result: Dict) -> JourneyAttribute:
        try:
            return convert_here_result_to_journey_attribute(result)
        except (KeyError, IndexError, ApiFail) as e:
            raise ApiFail(f"Failed to parse {self.display_name} response: {str(e)}")


class HereIntermodalAPI(BaseHereAPI[HereIntermodalRequestParameters, JourneyAttribute]):
    display_name = "HERE Intermodal API"
    root_url = "https://intermodal.router.hereapi.com/v8/routes"

    def compute_route(
        self, parameters: HereIntermodalRequestParameters
    ) -> JourneyAttribute:
        params = self._format_input_parameters(parameters)
        result = self._make_request(self.root_url, params)
        return self._format_result(result)

    def _format_input_parameters(
        self, parameters: HereIntermodalRequestParameters
    ) -> Dict:
        origin_str = f"{parameters.origin.latitude},{parameters.origin.longitude}"
        dest_str = (
            f"{parameters.destination.latitude},{parameters.destination.longitude}"
        )
        check_max_transfers_is_in_range(parameters.max_transfers)

        params = {
            "origin": origin_str,
            "destination": dest_str,
            "return": ",".join(parameters.return_values),
            "arrivalTime": parameters.arrival_time.isoformat(),
            "changes": parameters.max_transfers,
            "pedestrian[speed]": parameters.walk_speed,
            "bicycle[maxDistance]": parameters.bicycle_max_distance,
            "vehicle[enable]": parameters.private_vehicle_enabled.value,
            "vehicle[modes]": parameters.vehicle_mode.value,
        }

        transit_modes = []
        if len(parameters.include_transit_modes) > 0:
            transit_modes.extend(
                [mode.value for mode in parameters.include_transit_modes]
            )
        if len(parameters.exclude_transit_modes) > 0:
            transit_modes.extend(
                [f"-{mode.value}" for mode in parameters.exclude_transit_modes]
            )
        if transit_modes:
            params["transit[modes]"] = ",".join(transit_modes)

        vehicle_modes = []
        if len(parameters.include_vehicle_modes) > 0:
            vehicle_modes.extend(
                [mode.value for mode in parameters.include_vehicle_modes]
            )
        if len(parameters.exclude_vehicle_modes) > 0:
            vehicle_modes.extend(
                [f"-{mode.value}" for mode in parameters.exclude_vehicle_modes]
            )
        if vehicle_modes:
            params["vehicle[modes]"] = ",".join(vehicle_modes)

        return params

    def _format_result(self, result: Dict) -> JourneyAttribute:
        try:
            return convert_here_result_to_journey_attribute(result)
        except (KeyError, IndexError, ApiFail) as e:
            raise ApiFail(f"Failed to parse {self.display_name} response: {str(e)}")


class HereIsolineAPI(BaseHereAPI[HereIsolineRequestParameters, BaseGeometry]):
    display_name = "HERE Isoline API"
    root_url = "https://isoline.router.hereapi.com/v8/isolines"

    def compute_isoline(self, parameters: HereIsolineRequestParameters) -> BaseGeometry:
        if parameters.mode is None or parameters.range_values is None:
            raise ApiFail("mode, and range_values are required for isoline computation")

        params = self._format_input_parameters(parameters)
        result = self._make_request(self.root_url, params)
        return self._format_result(result)

    def _format_input_parameters(
        self, parameters: HereIsolineRequestParameters
    ) -> Dict:
        params = {
            "arrivalTime": parameters.arrival_time.isoformat(),
            "transportMode": self._map_here_mode(parameters.mode),
            "destination": f"{parameters.center_point.latitude},{parameters.center_point.longitude}",
            "range[type]": parameters.range_type.value,
            "range[values]": ",".join(map(str, parameters.range_values)),
            "routingMode": parameters.routing_mode.value,
        }
        return params

    def _format_result(self, result: Dict) -> BaseGeometry:
        try:
            isoline = result["isolines"][0]
            polyline = isoline["polygons"][0]["outer"]
            decoded_polyline = flexpolyline.decode(polyline)
            coordinates = [[point[1], point[0]] for point in decoded_polyline]

            return shape(
                {
                    "type": "Polygon",
                    "coordinates": [coordinates],
                }
            )

        except (KeyError, IndexError) as e:
            raise ApiFail(f"Failed to parse {self.display_name} response: {str(e)}")


def _validate_here_routes(result: Dict) -> Dict:
    routes = result.get("routes", [])
    if len(routes) == 0:
        notices = result.get("notices", [])
        if len(notices) == 0:
            message = "No route found"
        else:
            titles = [notice.get("title", "No route found") for notice in notices]
            message = "\n".join(titles)
        raise ApiFail(message)
    if len(routes) > 1:
        raise ApiFail("Multiple routes returned")
    return routes[0]


def _extract_route_distance(sections: List[Dict]) -> int:
    distance = 0
    for section in sections:
        distance += section["travelSummary"]["length"]
    return distance


def _calculate_total_duration_from_timestamps(sections: List[Dict]) -> int:

    try:
        first_departure_str = str(sections[0]["departure"]["time"])
        first_departure = datetime.fromisoformat(first_departure_str)

        last_arrival_str = str(sections[-1]["arrival"]["time"])
        last_arrival = datetime.fromisoformat(last_arrival_str)

        return int((last_arrival - first_departure).total_seconds())
    except (KeyError, ValueError):
        raise ApiFail("Invalid section timestamps in HERE response")


def convert_here_result_to_journey_attribute(result: Dict) -> JourneyAttribute:
    route = _validate_here_routes(result)
    sections = route["sections"]

    distance = _extract_route_distance(sections)
    duration = _calculate_total_duration_from_timestamps(sections)

    return JourneyAttribute(
        duration=int(duration),
        distance=int(distance),
        emission=None,
    )


def check_max_transfers_is_in_range(max_transfers: Optional[int]) -> None:
    """
    HERE Public Transit API and Intermodal API allow a maximum of 6 transfers. [0... 6]
    """
    if max_transfers is None:
        return
    if not (0 <= max_transfers <= 6):
        raise ValueError(
            "max_transfers must be between 0 and 6 for HERE public transport routing"
        )
