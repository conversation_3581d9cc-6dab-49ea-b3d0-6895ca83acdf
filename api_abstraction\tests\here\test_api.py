from datetime import datetime
from typing import Any
from unittest.mock import Mock, patch

import pytest

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.here.api import HereTravelTimeAPI
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.here import (
    HereIntermodalRequestParameters,
    HereIsolineRequestParameters,
    HerePublicTransitRequestParameters,
    HereRoutingRequestParameters,
    RangeType,
    RoutingMode,
)
from mobility.ir.transport import TransportMode


def make_geo_coordinates(lat: float = 1.0, lon: float = 2.0) -> GeoCoordinates:
    return GeoCoordinates(latitude=lat, longitude=lon)


def make_od(
    origin: GeoCoordinates = make_geo_coordinates(),
    destination: GeoCoordinates = make_geo_coordinates(3.0, 4.0),
) -> tuple[GeoCoordinates, GeoCoordinates]:
    return origin, destination


@pytest.fixture
def here_api() -> HereTravelTimeAPI:
    with (
        patch("api_abstraction.here.api.HereRoutingAPI"),
        patch("api_abstraction.here.api.HereIsolineAPI"),
        patch("api_abstraction.here.api.HerePublicTransitAPI"),
        patch("api_abstraction.here.api.HereIntermodalAPI"),
        patch("api_abstraction.here.api.logging"),
    ):
        api = HereTravelTimeAPI("test_token", EventReporter())
        return api


class TestHereTravelTimeAPI:
    def test_time_catches_exceptions_and_raises_api_fail(
        self,
        here_api: HereTravelTimeAPI,
    ) -> None:
        origin, destination = make_od()
        mode = TransportMode.CAR
        arrival_time = (8, 30)
        error_message = "APIFAIL"

        with patch.object(here_api, "_time_at", side_effect=Exception(error_message)):
            with pytest.raises(
                ApiFail, match=f"HERE Travel Time API request failed: {error_message}"
            ):
                here_api._time(origin, destination, mode, arrival_time)

    @patch.object(HereTravelTimeAPI, "_create_arrival_datetime")
    def test_time_at_uses_car_mode(
        self,
        mock_datetime: Any,
        here_api: HereTravelTimeAPI,
    ) -> None:
        origin, destination = make_od()
        mode = TransportMode.CAR
        arrival_time = (8, 30)
        arrival_datetime = datetime(2025, 4, 30, *arrival_time)
        mock_datetime.return_value = arrival_datetime
        with patch.object(
            here_api.routing_api, "compute_route", return_value=Mock()
        ) as mock_compute_route:
            here_api._time_at(origin, destination, mode, arrival_time)

        mock_compute_route.assert_called_once()
        call_args = mock_compute_route.call_args[0][0]
        assert isinstance(call_args, HereRoutingRequestParameters)
        assert call_args.origin == origin
        assert call_args.destination == destination
        assert call_args.mode == mode
        assert call_args.arrival_time == arrival_datetime
        assert call_args.routing_mode == RoutingMode.FAST

    @patch.object(HereTravelTimeAPI, "_create_arrival_datetime")
    def test_time_at_uses_intermodal_api_for_public_transport(
        self,
        mock_datetime: Any,
        here_api: HereTravelTimeAPI,
    ) -> None:
        origin, destination = make_od()
        mode = TransportMode.PUBLIC_TRANSPORT
        arrival_time = (8, 30)
        arrival_datetime = datetime(2025, 4, 30, *arrival_time)
        mock_datetime.return_value = arrival_datetime
        here_api.use_intermodal_api = True

        with patch.object(
            here_api.intermodal_api, "compute_route", return_value=Mock()
        ) as mock_compute_route:
            here_api._time_at(origin, destination, mode, arrival_time)

        mock_compute_route.assert_called_once()
        call_args = mock_compute_route.call_args[0][0]
        assert isinstance(call_args, HereIntermodalRequestParameters)
        assert call_args.origin == origin
        assert call_args.destination == destination
        assert call_args.mode == mode
        assert call_args.arrival_time == arrival_datetime
        assert call_args.walk_speed == 1.12

    @patch.object(HereTravelTimeAPI, "_create_arrival_datetime")
    def test_time_at_uses_transit_api_when_intermodal_disabled(
        self,
        mock_datetime: Any,
        here_api: HereTravelTimeAPI,
    ) -> None:
        origin, destination = make_od()
        mode = TransportMode.PUBLIC_TRANSPORT
        arrival_time = (8, 30)
        here_api.use_intermodal_api = False
        arrival_datetime = datetime(2025, 4, 30, *arrival_time)
        mock_datetime.return_value = arrival_datetime

        with patch.object(
            here_api.transit_api, "compute_route", return_value=Mock()
        ) as mock_compute_route:
            here_api._time_at(origin, destination, mode, arrival_time)

        mock_compute_route.assert_called_once()
        call_args = mock_compute_route.call_args[0][0]
        assert isinstance(call_args, HerePublicTransitRequestParameters)
        assert call_args.origin == origin
        assert call_args.destination == destination
        assert call_args.mode == mode
        assert call_args.arrival_time == arrival_datetime
        assert call_args.walk_speed == 1.12

    def test_compute_isochrone_raises_api_inapt_for_public_transport(
        self,
        here_api: HereTravelTimeAPI,
    ) -> None:
        _, destination = make_od()
        mode = TransportMode.PUBLIC_TRANSPORT
        boundary = 600

        with pytest.raises(
            ApiInapt, match="HERE API does not support public transport isochrones"
        ):
            here_api.compute_isochrone(Mock(), destination, mode, boundary)

    @patch("api_abstraction.here.api.geopandas")
    @patch("api_abstraction.here.api.json")
    def test_compute_isochrone_returns_geometry(
        self,
        mock_json: Any,
        mock_geopandas: Any,
        here_api: HereTravelTimeAPI,
    ) -> None:
        _, destination = make_od()
        fake_geometry = {"type": "Polygon", "coordinates": [[1.0, 2.0]]}
        mock_geopandas.GeoSeries.return_value.to_json.return_value = (
            f'{{"features":[{{"geometry":{fake_geometry}}}]}}'
        )
        mock_json.loads.return_value = {"features": [{"geometry": fake_geometry}]}

        with patch.object(
            here_api.isoline_api, "compute_isoline", return_value=Mock()
        ) as mock_compute_isoline:
            result = here_api.compute_isochrone(
                Mock(), destination, TransportMode.CAR, 600
            )

        assert result == fake_geometry
        mock_compute_isoline.assert_called_once()

    @patch.object(HereTravelTimeAPI, "_create_arrival_datetime")
    def test_compute_isochrone_with_arrival_time(
        self,
        mock_datetime: Any,
        here_api: HereTravelTimeAPI,
    ) -> None:
        _, destination = make_od()
        mode = TransportMode.CAR
        boundary = 600
        arrival_time = (8, 30)
        arrival_datetime = datetime(2025, 4, 30, *arrival_time)
        mock_datetime.return_value = arrival_datetime

        with patch.object(
            here_api.isoline_api, "compute_isoline", return_value=Mock()
        ) as mock_compute_isoline:
            here_api._compute_isochrone_with_arrival_time(destination, mode, boundary)

        mock_compute_isoline.assert_called_once()
        call_args = mock_compute_isoline.call_args[0][0]
        assert isinstance(call_args, HereIsolineRequestParameters)
        assert call_args.center_point == destination
        assert call_args.arrival_time == arrival_datetime
        assert call_args.mode == mode
        assert call_args.range_type == RangeType.TIME
        assert call_args.range_values == [boundary]

    def test_compute_isochrone_logs_warning_on_exception(
        self,
        here_api: HereTravelTimeAPI,
    ) -> None:
        _, destination = make_od()
        mode = TransportMode.CAR
        boundary = 600
        error_message = "APIFAIL"
        here_api.logger = Mock()

        with patch.object(
            here_api.isoline_api,
            "compute_isoline",
            side_effect=Exception(error_message),
        ):
            _ = here_api.compute_isochrone(Mock(), destination, mode, boundary)
            here_api.logger.warning.assert_called_once_with(
                f"Failed to compute isochrone: {error_message}"
            )
