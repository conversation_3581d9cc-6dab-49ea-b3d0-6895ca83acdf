from typing import Any
from unittest.mock import Mock, patch

import pytest

from api_abstraction.api.api import ApiFail
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.lux_geocode.lux_geocode import LuxGeocoder


class TestLuxGeocodeApi:
    @patch("api_abstraction.lux_geocode.lux_geocode.requests")
    def test_lux_geocode_api_geocodes_the_address_perfectly_thanks(
        self, mocked_requests: Any, geo_coordinates: Any
    ) -> None:
        address = "rue du port"
        result = {
            "success": True,
            "count": 1,
            "request": "rue du port",
            "results": [
                {
                    "name": " ,  rosport ",
                    "accuracy": 5,
                    "address": "rosport,Luxembourg",
                    "country": "Luxembourg",
                    "matching street": None,
                    "ratio": 0,
                    "easting": 104099.734642587,
                    "northing": 96856.5563566511,
                    "geom": {
                        "type": "Point",
                        "coordinates": [104099.734642587, 96856.556356651],
                    },
                    "geomlonlat": {
                        "type": "Point",
                        "coordinates": [6.502915619, 49.805652317],
                    },
                    "AddressDetails": {
                        "postnumber": None,
                        "street": None,
                        "zip": None,
                        "locality": "rosport",
                    },
                }
            ],
        }
        request_returned_value = Mock()
        request_returned_value.json.return_value = result
        mocked_requests.get.return_value = request_returned_value
        api = LuxGeocoder("", EventReporter())

        geocoded_addresss = api.geocode(address)

        assert geocoded_addresss == geo_coordinates(
            latitude=49.805652317, longitude=6.502915619
        )
        mocked_requests.get.assert_called_with(
            api.lux_api_url, params={"queryString": address}
        )

    @patch("api_abstraction.lux_geocode.lux_geocode.requests")
    def test_lux_geocode_api_fails_when_the_lux_api_did_not_compute_geocoordinates(
        self, mocked_requests: Any, geo_coordinates: Any
    ) -> None:
        address = ""
        result = {
            "success": True,
            "count": 1,
            "request": "",
            "results": [
                {
                    "name": " ,   ",
                    "accuracy": 1,
                    "address": "Luxembourg",
                    "country": "Luxembourg",
                    "matching street": None,
                    "ratio": 0,
                    "geom": None,
                    "AddressDetails": {
                        "postnumber": None,
                        "street": None,
                        "zip": None,
                        "locality": None,
                    },
                }
            ],
        }
        request_returned_value = Mock()
        request_returned_value.json.return_value = result
        mocked_requests.get.return_value = request_returned_value
        api = LuxGeocoder("", EventReporter())

        with pytest.raises(ApiFail):
            api.geocode(address)

    @patch("api_abstraction.lux_geocode.lux_geocode.requests")
    def test_lux_geocode_api_geocodes_the_details_of_the_address(
        self,
        mocked_requests: Any,
        address: Any,
        geo_coordinates: Any,
    ) -> None:
        input_address = "Rue Willy Goergen, Luxembourg"
        result = {
            "success": True,
            "count": 1,
            "request": "Rue Willy Goergen, Luxembourg",
            "results": [
                {
                    "name": " , Rue Willy Goergen 1636 Luxembourg",
                    "accuracy": 6,
                    "address": ", Rue Willy Goergen 1636 Luxembourg",
                    "matching street": "Rue Willy Goergen",
                    "ratio": 1.0,
                    "easting": 77245.7964166667,
                    "northing": 75456.9042162117,
                    "geom": {
                        "type": "Point",
                        "coordinates": [77245.796416667, 75456.904216212],
                    },
                    "geomlonlat": {
                        "type": "Point",
                        "coordinates": [6.130012376, 49.61373157],
                    },
                    "AddressDetails": {
                        "postnumber": "",
                        "street": "Rue Willy Goergen",
                        "zip": "1636",
                        "locality": "Luxembourg",
                        "id_caclr_street": "613",
                        "id_caclr_building": "",
                        "id_caclr_locality": "1",
                    },
                }
            ],
        }
        request_returned_value = Mock()
        request_returned_value.json.return_value = result
        mocked_requests.get.return_value = request_returned_value
        api = LuxGeocoder("", EventReporter())

        geocoded_addresss = api.geocode_details(input_address)

        expected_address = address(
            full=input_address,
            normalized=", Rue Willy Goergen 1636 Luxembourg",
            city="Luxembourg",
            postcode="1636",
            citycode="1636",
            coordinates=geo_coordinates(latitude=49.61373157, longitude=6.130012376),
        )
        assert geocoded_addresss == expected_address
        mocked_requests.get.assert_called_with(
            api.lux_api_url, params={"queryString": input_address}
        )

    @patch("api_abstraction.lux_geocode.lux_geocode.requests")
    def test_lux_geocode_api_should_fail_if_zip_code_is_none(
        self,
        mocked_requests: Any,
        address: Any,
        geo_coordinates: Any,
    ) -> None:
        input_address = "Luxembourg"
        result = {
            "success": True,
            "count": 1,
            "request": "Luxembourg",
            "results": [
                {
                    "name": " ,  luxembourg ",
                    "accuracy": 5,
                    "address": "luxembourg,Luxembourg",
                    "country": "Luxembourg",
                    "matching street": None,
                    "ratio": 0,
                    "easting": 77285.7822943538,
                    "northing": 75176.1427335889,
                    "geom": {
                        "type": "Point",
                        "coordinates": [77285.782294354, 75176.142733589],
                    },
                    "geomlonlat": {
                        "type": "Point",
                        "coordinates": [6.130567561, 49.611207422],
                    },
                    "AddressDetails": {
                        "postnumber": None,
                        "street": None,
                        "zip": None,
                        "locality": "luxembourg",
                    },
                }
            ],
        }
        request_returned_value = Mock()
        request_returned_value.json.return_value = result
        mocked_requests.get.return_value = request_returned_value
        api = LuxGeocoder("", EventReporter())

        with pytest.raises(ApiFail):
            api.geocode_details(input_address)
