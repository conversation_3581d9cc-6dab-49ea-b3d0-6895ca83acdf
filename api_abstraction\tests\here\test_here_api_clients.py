from datetime import datetime
from typing import Any, Dict
from unittest.mock import Mock, patch

import pytest
from shapely.geometry import Polygon

from api_abstraction.api.api import ApiFail
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.here.here_api_clients import (
    HereIntermodalAPI,
    HereIsolineAPI,
    HerePublicTransitAPI,
    HereRoutingAPI,
    _calculate_total_duration_from_timestamps,
    _extract_route_distance,
    _validate_here_routes,
    check_max_transfers_is_in_range,
    convert_here_result_to_journey_attribute,
)
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.here import (
    HereIntermodalRequestParameters,
    HereIsolineRequestParameters,
    HerePublicTransitRequestParameters,
    HereRoutingRequestParameters,
    PrivateVehicleEnabled,
    RangeType,
    RoutingMode,
    TransitMode,
    VehicleMode,
)
from mobility.ir.transport import TransportMode


def make_geo_coordinates(lat: float = 1.0, lon: float = 2.0) -> GeoCoordinates:
    return GeoCoordinates(latitude=lat, longitude=lon)


def make_routing_request_params(
    origin=make_geo_coordinates(),
    destination=make_geo_coordinates(3.0, 4.0),
    mode=TransportMode.CAR,
    arrival_time=datetime(2025, 1, 1, 8, 30),
    routing_mode=RoutingMode.FAST,
    return_values=["travelSummary"],
) -> HereRoutingRequestParameters:
    return HereRoutingRequestParameters(
        origin=origin,
        destination=destination,
        mode=mode,
        arrival_time=arrival_time,
        routing_mode=routing_mode,
        return_values=return_values,
    )


def make_public_transit_request_params(
    origin=GeoCoordinates(latitude=1.0, longitude=2.0),
    destination=GeoCoordinates(latitude=3.0, longitude=4.0),
    mode=TransportMode.PUBLIC_TRANSPORT,
    arrival_time=datetime(2025, 1, 1, 8, 30),
    max_transfers=2,
    walk_speed=1.5,
) -> HerePublicTransitRequestParameters:
    return HerePublicTransitRequestParameters(
        origin=origin,
        destination=destination,
        mode=mode,
        arrival_time=arrival_time,
        max_transfers=max_transfers,
        walk_speed=walk_speed,
    )


def make_intermodal_request_params(
    origin=GeoCoordinates(latitude=1.0, longitude=2.0),
    destination=GeoCoordinates(latitude=3.0, longitude=4.0),
    mode=TransportMode.PUBLIC_TRANSPORT,
    arrival_time=datetime(2025, 1, 1, 8, 30),
    max_transfers=2,
    walk_speed=1.5,
    bicycle_max_distance=10000,
    private_vehicle_enabled=PrivateVehicleEnabled.ROUTEHEAD,
) -> HereIntermodalRequestParameters:
    return HereIntermodalRequestParameters(
        origin=origin,
        destination=destination,
        mode=mode,
        arrival_time=arrival_time,
        max_transfers=max_transfers,
        walk_speed=walk_speed,
        bicycle_max_distance=bicycle_max_distance,
        private_vehicle_enabled=private_vehicle_enabled,
    )


def make_isoline_request_params(
    center_point=GeoCoordinates(latitude=1.0, longitude=2.0),
    mode=TransportMode.CAR,
    arrival_time=datetime(2025, 1, 1, 8, 30),
    range_values=[900],
    range_type=RangeType.TIME,
    routing_mode=RoutingMode.FAST,
) -> HereIsolineRequestParameters:
    return HereIsolineRequestParameters(
        center_point=center_point,
        mode=mode,
        arrival_time=arrival_time,
        range_values=range_values,
        range_type=range_type,
        routing_mode=routing_mode,
    )


class TestHereRoutingAPI:
    def test_format_input_parameters_with_missing_mode_should_raise(self) -> None:
        api = HereRoutingAPI("test_api_key")
        params = make_routing_request_params(mode=None)

        with pytest.raises(ApiFail, match="mode is required for route computation"):
            api._format_input_parameters(params)

    def test_format_input_parameters_should_return_correct_dict(self) -> None:
        api = HereRoutingAPI("test_api_key")
        params = make_routing_request_params()

        result = api._format_input_parameters(params)

        assert result == {
            "transportMode": "car",
            "origin": "1.0,2.0",
            "destination": "3.0,4.0",
            "return": "travelSummary",
            "routingMode": "fast",
            "arrivalTime": "2025-01-01T08:30:00",
        }

    @patch.object(HereRoutingAPI, "_make_request")
    @patch.object(HereRoutingAPI, "_format_result")
    def test_compute_route_should_return_journey_attribute(
        self, mock_format_result: Mock, mock_make_request: Mock
    ) -> None:
        api = HereRoutingAPI("test_api_key")
        params = make_routing_request_params()
        mock_response = {
            "routes": [
                {"sections": [{"travelSummary": {"duration": 300, "length": 5000}}]}
            ]
        }
        mock_make_request.return_value = mock_response
        mock_format_result.return_value = JourneyAttribute(
            duration=300, distance=5000, emission=None
        )

        result = api.compute_route(params)

        mock_make_request.assert_called_once()
        mock_format_result.assert_called_once_with(mock_response)
        assert result == JourneyAttribute(duration=300, distance=5000, emission=None)

    def test_format_result_should_return_journey_attribute(self) -> None:
        api = HereRoutingAPI("test_api_key")
        result = {
            "routes": [
                {
                    "sections": [
                        {
                            "travelSummary": {"length": 5000},
                            "departure": {"time": "2025-06-20T08:48:00+02:00"},
                            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
                        }
                    ]
                }
            ]
        }

        journey = api._format_result(result)

        assert journey == JourneyAttribute(duration=120, distance=5000, emission=None)

    def test_format_result_with_invalid_structure_should_raise(self) -> None:
        api = HereRoutingAPI("test_api_key")
        result = {"invalid": "structure"}

        with pytest.raises(ApiFail, match="Failed to parse HERE Routing API response"):
            api._format_result(result)


class TestHerePublicTransitAPI:
    def test_format_input_parameters_should_return_correct_dict(self) -> None:
        api = HerePublicTransitAPI("test_api_key")
        params = make_public_transit_request_params()

        result = api._format_input_parameters(params)

        assert result == {
            "origin": "1.0,2.0",
            "destination": "3.0,4.0",
            "return": "travelSummary",
            "arrivalTime": "2025-01-01T08:30:00",
            "changes": 2,
            "pedestrian[speed]": 1.5,
        }

    def test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes(
        self,
    ) -> None:
        api = HerePublicTransitAPI("test_api_key")
        params = make_public_transit_request_params()
        params.include_transit_modes = (TransitMode.CITY_TRAIN, TransitMode.SUBWAY)
        params.exclude_transit_modes = (TransitMode.FLIGHT, TransitMode.SPACESHIP)

        result = api._format_input_parameters(params)

        assert "transit[modes]" in result
        assert result["transit[modes]"] == "cityTrain,subway,-flight,-spaceship"

    @patch.object(HerePublicTransitAPI, "_make_request")
    @patch.object(HerePublicTransitAPI, "_format_result")
    def test_compute_route_should_return_journey_attribute(
        self, mock_format_result: Mock, mock_make_request: Mock
    ) -> None:
        api = HerePublicTransitAPI("test_api_key")
        params = make_public_transit_request_params()
        mock_response = {
            "routes": [
                {"sections": [{"travelSummary": {"duration": 300, "length": 5000}}]}
            ]
        }
        mock_make_request.return_value = mock_response
        mock_format_result.return_value = JourneyAttribute(
            duration=300, distance=5000, emission=None
        )

        result = api.compute_route(params)

        mock_make_request.assert_called_once()
        mock_format_result.assert_called_once_with(mock_response)
        assert result == JourneyAttribute(duration=300, distance=5000, emission=None)

    def test_format_result_should_return_journey_attribute(self) -> None:
        api = HerePublicTransitAPI("test_api_key")
        result = {
            "routes": [
                {
                    "sections": [
                        {
                            "travelSummary": {"length": 5000},
                            "departure": {"time": "2025-06-20T08:48:00+02:00"},
                            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
                        }
                    ]
                }
            ]
        }

        journey = api._format_result(result)

        assert journey == JourneyAttribute(duration=120, distance=5000, emission=None)

    def test_format_result_with_invalid_structure_should_raise(self) -> None:
        api = HerePublicTransitAPI("test_api_key")
        result = {"invalid": "structure"}

        with pytest.raises(ApiFail, match="Failed to parse HERE Transit API response"):
            api._format_result(result)

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_public_transit_api_should_send_max_transfers(self, mock_get: Any) -> None:
        api = HerePublicTransitAPI("test_api_key")
        params = make_public_transit_request_params(max_transfers=3)
        expected_params = {
            "origin": "1.0,2.0",
            "destination": "3.0,4.0",
            "return": "travelSummary",
            "arrivalTime": "2025-01-01T08:30:00",
            "changes": 3,
            "pedestrian[speed]": 1.5,
            "apikey": "test_api_key",
        }
        mock_response = mock_get.return_value
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "routes": [
                {
                    "sections": [
                        {
                            "travelSummary": {"length": 1000},
                            "departure": {"time": "2025-06-20T08:48:00+02:00"},
                            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
                        }
                    ]
                }
            ]
        }

        api.compute_route(params)

        mock_get.assert_called_once()
        called_url, called_kwargs = mock_get.call_args
        assert called_url[0] == api.root_url
        for k, v in expected_params.items():
            assert called_kwargs["params"][k] == v


class TestHereIntermodalAPI:
    def test_format_input_parameters_should_return_correct_dict(self) -> None:
        api = HereIntermodalAPI("test_api_key")
        params = make_intermodal_request_params()

        result = api._format_input_parameters(params)

        assert result == {
            "origin": "1.0,2.0",
            "destination": "3.0,4.0",
            "return": "travelSummary",
            "arrivalTime": "2025-01-01T08:30:00",
            "changes": 2,
            "pedestrian[speed]": 1.5,
            "bicycle[maxDistance]": 10000,
            "vehicle[enable]": "routeHead",
            "vehicle[modes]": "car",
        }

    def test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes(
        self,
    ) -> None:
        api = HereIntermodalAPI("test_api_key")
        params = make_intermodal_request_params()
        params.include_transit_modes = (TransitMode.CITY_TRAIN, TransitMode.SUBWAY)
        params.exclude_transit_modes = (TransitMode.FLIGHT, TransitMode.SPACESHIP)

        result = api._format_input_parameters(params)

        assert "transit[modes]" in result
        assert result["transit[modes]"] == "cityTrain,subway,-flight,-spaceship"

    def test_format_input_parameters_should_handle_both_include_and_exclude_vehicle_modes(
        self,
    ) -> None:
        api = HereIntermodalAPI("test_api_key")
        params = make_intermodal_request_params()
        params.include_vehicle_modes = (VehicleMode.BICYCLE, VehicleMode.CAR)
        params.exclude_vehicle_modes = (VehicleMode.KICK_SCOOTER,)

        result = api._format_input_parameters(params)

        assert "vehicle[modes]" in result
        assert result["vehicle[modes]"] == "bicycle,car,-kickScooter"

    @patch.object(HereIntermodalAPI, "_make_request")
    @patch.object(HereIntermodalAPI, "_format_result")
    def test_compute_route_should_return_journey_attribute(
        self, mock_format_result: Mock, mock_make_request: Mock
    ) -> None:
        api = HereIntermodalAPI("test_api_key")
        params = make_intermodal_request_params()
        mock_response = {
            "routes": [
                {"sections": [{"travelSummary": {"duration": 300, "length": 5000}}]}
            ]
        }
        mock_make_request.return_value = mock_response
        mock_format_result.return_value = JourneyAttribute(
            duration=300, distance=5000, emission=None
        )

        result = api.compute_route(params)

        mock_make_request.assert_called_once()
        mock_format_result.assert_called_once_with(mock_response)
        assert result == JourneyAttribute(duration=300, distance=5000, emission=None)

    def test_format_result_should_return_journey_attribute(self) -> None:
        api = HereIntermodalAPI("test_api_key")
        result = {
            "routes": [
                {
                    "sections": [
                        {
                            "travelSummary": {"length": 5000},
                            "departure": {"time": "2025-06-20T08:48:00+02:00"},
                            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
                        }
                    ]
                }
            ]
        }

        journey = api._format_result(result)

        assert journey == JourneyAttribute(duration=120, distance=5000, emission=None)

    def test_format_result_with_invalid_structure_should_raise(self) -> None:
        api = HereIntermodalAPI("test_api_key")
        result = {"invalid": "structure"}

        with pytest.raises(
            ApiFail, match="Failed to parse HERE Intermodal API response"
        ):
            api._format_result(result)


class TestHereIsolineAPI:
    def test_compute_isoline_without_mode_should_raise(self) -> None:
        api = HereIsolineAPI("test_api_key")
        params = make_isoline_request_params(mode=None)

        with pytest.raises(
            ApiFail, match="mode, and range_values are required for isoline computation"
        ):
            api.compute_isoline(params)

    def test_compute_isoline_without_range_values_should_raise(self) -> None:
        api = HereIsolineAPI("test_api_key")
        params = make_isoline_request_params(range_values=None)

        with pytest.raises(
            ApiFail, match="mode, and range_values are required for isoline computation"
        ):
            api.compute_isoline(params)

    def test_format_input_parameters_should_return_correct_dict(self) -> None:
        api = HereIsolineAPI("test_api_key")
        params = make_isoline_request_params()

        result = api._format_input_parameters(params)

        assert result == {
            "arrivalTime": "2025-01-01T08:30:00",
            "transportMode": "car",
            "destination": "1.0,2.0",
            "range[type]": "time",
            "range[values]": "900",
            "routingMode": "fast",
        }

    def test_format_result_should_return_polygon(self) -> None:
        api = HereIsolineAPI("test_api_key")
        mock_result = {"isolines": [{"polygons": [{"outer": "tuhryjdhv"}]}]}

        with patch(
            "flexpolyline.decode",
            return_value=[[1.0, 2.0], [3.0, 4.0], [3.0, 2.0], [1.0, 2.0]],
        ):
            result = api._format_result(mock_result)

            assert isinstance(result, Polygon)
            assert result.geom_type == "Polygon"

    def test_format_result_with_invalid_structure_should_raise(self) -> None:
        api = HereIsolineAPI("test_api_key")
        result = {"invalid": "structure"}

        with pytest.raises(ApiFail, match="Failed to parse HERE Isoline API response"):
            api._format_result(result)


def test_validate_here_routes_with_no_routes_and_no_notices_should_raise_apifail() -> (
    None
):
    result: Dict = {"routes": []}

    with pytest.raises(ApiFail, match="No route found"):
        _validate_here_routes(result)


def test_validate_here_routes_with_single_route_should_return_route() -> None:
    expected_route = {
        "sections": [{"travelSummary": {"duration": 100, "length": 1000}}]
    }
    result = {"routes": [expected_route]}

    route = _validate_here_routes(result)

    assert route == expected_route


def test_extract_route_distance_should_sum_lengths_of_sections() -> None:
    sections = [
        {"travelSummary": {"length": 5000}},
        {"travelSummary": {"length": 3000}},
    ]

    distance = _extract_route_distance(sections)

    assert distance == 8000


def test_calculate_total_duration_from_timestamps_should_return_correct_duration() -> (
    None
):
    sections = [
        {
            "departure": {"time": "2025-06-20T08:48:00+02:00"},
            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
        },
        {
            "departure": {"time": "2025-06-20T09:00:00+02:00"},
            "arrival": {"time": "2025-06-20T09:05:00+02:00"},
        },
    ]

    duration = _calculate_total_duration_from_timestamps(sections)

    assert duration == 1020


def test_calculate_total_duration_from_timestamps_with_invalid_data_should_raise_error() -> (
    None
):
    sections = [
        {
            "departure": {"time": "invalid"},
            "arrival": {"time": "2025-06-20T08:50:00+02:00"},
        },
    ]

    with pytest.raises(ApiFail, match="Invalid section timestamps in HERE response"):
        _calculate_total_duration_from_timestamps(sections)


def test_calculate_total_duration_from_timestamps_with_missing_fields_should_raise_error() -> (
    None
):
    sections = [{"departure": {"time": "2025-06-20T08:48:00+02:00"}}]

    with pytest.raises(ApiFail, match="Invalid section timestamps in HERE response"):
        _calculate_total_duration_from_timestamps(sections)


@pytest.mark.parametrize(
    "result",
    [
        {"routes": [{"not_sections": []}]},
        {"routes": [{"sections": [{"not_travelSummary": {}}]}]},
        {
            "routes": [
                {
                    "sections": [
                        {
                            "travelSummary": {"length": 100},
                            "departure": {"time": "2025-06-20T08:48:00+02:00"},
                            "not_arrival": {},
                        }
                    ]
                }
            ]
        },
    ],
)
def test_result_with_invalid_structure_should_raise(result: Dict) -> None:
    with pytest.raises((KeyError, ApiFail)):
        convert_here_result_to_journey_attribute(result)


def test_empty_routes_and_no_notices_should_raise_apifail() -> None:
    result: Dict = {"routes": []}
    with pytest.raises(ApiFail, match="No route found"):
        convert_here_result_to_journey_attribute(result)


def test_empty_routes_with_notices_should_raise_apifail_with_titles() -> None:
    result: Dict = {
        "routes": [],
        "notices": [
            {"title": "Route not possible", "code": "noRouteFound"},
            {"title": "Another notice", "code": "otherCode"},
        ],
    }
    with pytest.raises(ApiFail, match="Route not possible\nAnother notice"):
        convert_here_result_to_journey_attribute(result)


def test_empty_routes_with_notices_missing_title_should_default() -> None:
    result: Dict = {
        "routes": [],
        "notices": [
            {"code": "noRouteFound"},
            {"title": "Another notice", "code": "otherCode"},
        ],
    }
    with pytest.raises(ApiFail, match="No route found\nAnother notice"):
        convert_here_result_to_journey_attribute(result)


def test_multiple_routes_should_raise_apifail() -> None:
    result: Dict = {
        "routes": [
            {
                "sections": [
                    {
                        "travelSummary": {"length": 1000},
                        "departure": {"time": "2025-06-20T08:48:00+02:00"},
                        "arrival": {"time": "2025-06-20T08:50:00+02:00"},
                    }
                ]
            },
            {
                "sections": [
                    {
                        "travelSummary": {"length": 2000},
                        "departure": {"time": "2025-06-20T08:48:00+02:00"},
                        "arrival": {"time": "2025-06-20T08:52:00+02:00"},
                    }
                ]
            },
        ]
    }
    with pytest.raises(ApiFail, match="Multiple routes returned"):
        convert_here_result_to_journey_attribute(result)


@pytest.mark.parametrize("value", [None, 0, 3, 6])
def test_check_max_transfers_is_in_range_with_acceptable_values_should_not_raise(value):
    check_max_transfers_is_in_range(value)


@pytest.mark.parametrize("value", [-1, 7, 100])
def test_check_max_transfers_is_in_range_should_raise_with_invalid_values(value):
    with pytest.raises(
        ValueError,
        match="max_transfers must be between 0 and 6 for HERE public transport routing",
    ):
        check_max_transfers_is_in_range(value)
