from unittest import mock
from typing import Any

import pytest

from mobility.funky import ImmutableDict
from mobility.ir.cost import Cost, Co<PERSON><PERSON><PERSON>, CostPayer, Costs
from mobility.ir.transport import TransportMode
from mobility.quantity import (
    adimensional,
    euros,
    gramEC,
    kilometers,
    minutes,
    seconds,
    trip,
    yearly,
)
from mobility.workers.costs import (
    BICYCLE_PARKING_YEARLY_COST,
    CAR_PARKING_COST,
    CostComputer,
    compute_absenteeism_cost_per_employee,
    compute_bicycle_km_fee,
    compute_bicycle_work_accident_cost_for_company,
    compute_bicycle_work_accident_cost_for_society,
    compute_car_congestion_damage_cost,
    compute_car_km_fee,
    compute_car_noise_damage_cost,
    compute_car_pollution_damage_cost,
    compute_car_work_accident_cost_for_company,
    compute_car_work_accident_cost_for_society,
    compute_carbone_cost,
    compute_company_attractiveness_cost_per_employee,
    compute_office_rent_cost,
    compute_pt_noise_damage_cost,
    compute_pt_pollution_damage_cost,
    compute_pt_subscription_cost,
    compute_pt_work_accident_cost_for_company,
    compute_pt_work_accident_cost_for_society,
    compute_sustainable_mobility_fee_for_company,
    compute_sustainable_mobility_fee_for_employee,
    compute_travel_time_cost,
    compute_walk_km_fee,
    compute_walk_work_accident_cost_for_company,
    compute_walk_work_accident_cost_for_society,
)


@pytest.fixture
def cost_computer():
    return CostComputer()


class TestCostsComputation:
    def test_should_compute_value_of_company_attractiveness_per_employee(self) -> None:
        cost = compute_company_attractiveness_cost_per_employee()

        assert -788 * euros / yearly <= cost <= -787 * euros / yearly

    def test_should_compute_value_of_absenteeism_per_employee(self) -> None:
        cost = compute_absenteeism_cost_per_employee()

        assert -268 * euros / yearly < cost < -267 * euros / yearly

    def test_should_compute_car_km_cost_for_no_range(self) -> None:
        cost = compute_car_km_fee(0 * kilometers)

        assert 1_456 * euros / yearly <= cost <= 1_457 * euros / yearly

    def test_should_compute_car_km_cost_for_short_range(self) -> None:
        cost = compute_car_km_fee(3 * kilometers)

        assert 1_734 * euros / yearly <= cost <= 1_735 * euros / yearly

    def test_should_compute_car_km_cost_for_medium_range(self) -> None:
        cost = compute_car_km_fee(10 * kilometers)

        assert 2341 * euros / yearly <= cost <= 2342 * euros / yearly

    def test_should_compute_car_km_cost_for_long_range(self) -> None:
        cost = compute_car_km_fee(40 * kilometers)

        assert 4538 * euros / yearly <= cost <= 4539 * euros / yearly


class TestNoiseCostComputation:
    def test_should_compute_car_noise_km_cost_for_no_range(self) -> None:
        cost = compute_car_noise_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_pt_noise_km_cost_for_no_range(self) -> None:
        cost = compute_pt_noise_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_noise_cost_for_short_range(self) -> None:
        cost = compute_car_noise_damage_cost(3 * kilometers)

        assert 4 * euros / yearly <= cost <= 5 * euros / yearly

    def test_should_compute_pt_noise_cost_for_short_range(self) -> None:
        cost = compute_pt_noise_damage_cost(3 * kilometers)

        assert 1 * euros / yearly <= cost <= 2 * euros / yearly

    def test_should_compute_car_noise_cost_for_medium_range(self) -> None:
        cost = compute_car_noise_damage_cost(10 * kilometers)

        assert 11 * euros / yearly <= cost <= 12 * euros / yearly

    def test_should_compute_pt_noise_cost_for_medium_range(self) -> None:
        cost = compute_pt_noise_damage_cost(10 * kilometers)

        assert 3 * euros / yearly <= cost <= 4 * euros / yearly

    def test_should_compute_car_noise_cost_for_long_range(self) -> None:
        cost = compute_car_noise_damage_cost(40 * kilometers)

        assert 19 * euros / yearly <= cost <= 20 * euros / yearly

    def test_should_compute_pt_noise_cost_for_long_range(self) -> None:
        cost = compute_pt_noise_damage_cost(40 * kilometers)

        assert 4 * euros / yearly <= cost <= 5 * euros / yearly


class TestPollutionCostComputation:
    def test_should_compute_car_pollution_km_cost_for_no_range(self) -> None:
        cost = compute_car_pollution_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_pt_pollution_km_cost_for_no_range(self) -> None:
        cost = compute_pt_pollution_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_pollution_cost_for_short_range(self) -> None:
        cost = compute_car_pollution_damage_cost(3 * kilometers)

        assert 243 * euros / yearly <= cost <= 244 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_short_range(self) -> None:
        cost = compute_pt_pollution_damage_cost(3 * kilometers)

        assert 72 * euros / yearly <= cost <= 73 * euros / yearly

    def test_should_compute_car_pollution_cost_for_medium_range(self) -> None:
        cost = compute_car_pollution_damage_cost(10 * kilometers)

        assert 451 * euros / yearly <= cost <= 452 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_medium_range(self) -> None:
        cost = compute_pt_pollution_damage_cost(10 * kilometers)

        assert 132 * euros / yearly <= cost <= 133 * euros / yearly

    def test_should_compute_car_pollution_cost_for_long_range(self) -> None:
        cost = compute_car_pollution_damage_cost(40 * kilometers)

        assert 666 * euros / yearly <= cost <= 667 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_long_range(self) -> None:
        cost = compute_pt_pollution_damage_cost(40 * kilometers)

        assert 173 * euros / yearly <= cost <= 174 * euros / yearly


class TestCongestionCostComputation:
    def test_should_compute_congestion_km_cost_for_no_range(self) -> None:
        cost = compute_car_congestion_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_congestion_cost_for_short_range(self) -> None:
        cost = compute_car_congestion_damage_cost(3 * kilometers)

        assert 602 * euros / yearly <= cost <= 603 * euros / yearly

    def test_should_compute_car_congestion_cost_for_medium_range(self) -> None:
        cost = compute_car_congestion_damage_cost(10 * kilometers)

        assert 1347 * euros / yearly <= cost <= 1348 * euros / yearly

    def test_should_compute_car_congestion_cost_for_long_range(self) -> None:
        cost = compute_car_congestion_damage_cost(40 * kilometers)

        assert 2034 * euros / yearly <= cost <= 2035 * euros / yearly


class TestCarbonCostComputation:
    def test_should_compute_carbon_cost_when_no_emission(self) -> None:
        cost = compute_carbone_cost(0 * gramEC)

        assert cost == 0 * euros / yearly

    def test_should_compute_carbon_cost(self) -> None:
        cost = compute_carbone_cost(1900 * gramEC)

        assert 109 * euros / yearly <= cost <= 110 * euros / yearly


class TestHealthImpactComputation:
    def test_should_compute_bc_health_impact_cost_when_no_travel(
        self, cost_computer
    ) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.BICYCLE, 0 * kilometers
        )

        assert len(cost) == 1
        assert cost[0].amount == 0 * euros / yearly

    def test_should_compute_map_health_impact_cost_when_no_travel(
        self, cost_computer
    ) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.WALK, 0 * kilometers
        )

        assert len(cost) == 1
        assert cost[0].amount == 0 * euros / yearly

    def test_should_compute_bc_health_impact_cost(self, cost_computer) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.BICYCLE, 10 * kilometers
        )

        assert len(cost) == 1
        assert -2432 * euros / yearly < cost[0].amount < -2431 * euros / yearly

    def test_should_compute_map_health_impact_cost(self, cost_computer) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.WALK, 10 * kilometers
        )

        assert len(cost) == 1
        assert -2432 * euros / yearly < cost[0].amount < -2431 * euros / yearly

    def test_should_compute_car_health_impact_cost(self, cost_computer) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.CAR, 10 * kilometers
        )

        assert cost == []

    def test_should_compute_pt_health_impact_cost(self, cost_computer) -> None:
        cost = cost_computer._compute_health_cost_for_society(
            TransportMode.PUBLIC_TRANSPORT, 10 * kilometers
        )

        assert cost == []


class TestWorkAccidentCostComputation:
    def test_should_compute_walk_work_accident_cost_for_society(self) -> None:
        cost = compute_walk_work_accident_cost_for_society(10 * kilometers)

        assert 940 * euros / yearly <= cost <= 941 * euros / yearly

    def test_should_compute_walk_work_accident_cost_for_company(self) -> None:
        cost = compute_walk_work_accident_cost_for_company(10 * kilometers)

        assert 103 * euros / yearly <= cost <= 104 * euros / yearly

    def test_should_compute_bicycle_work_accident_cost_for_society(self) -> None:
        cost = compute_bicycle_work_accident_cost_for_society(10 * kilometers)

        assert 1081 * euros / yearly <= cost <= 1082 * euros / yearly

    def test_should_compute_bicycle_work_accident_cost_for_company(self) -> None:
        cost = compute_bicycle_work_accident_cost_for_company(10 * kilometers)

        assert cost == 94 * euros / yearly

    def test_should_compute_pt_work_accident_cost_for_society(self) -> None:
        cost = compute_pt_work_accident_cost_for_society(10 * kilometers)

        assert 9 * euros / yearly <= cost <= 10 * euros / yearly

    def test_should_compute_pt_work_accident_cost_for_company(self) -> None:
        cost = compute_pt_work_accident_cost_for_company(10 * kilometers)

        assert 0 * euros / yearly <= cost <= 1 * euros / yearly

    def test_should_compute_car_work_accident_cost_for_society(self) -> None:
        cost = compute_car_work_accident_cost_for_society(10 * kilometers)

        assert 94 * euros / yearly <= cost <= 95 * euros / yearly

    def test_should_compute_car_work_accident_cost_for_company(self) -> None:
        cost = compute_car_work_accident_cost_for_company(10 * kilometers)

        assert 9 * euros / yearly <= cost <= 10 * euros / yearly


class TestTravelTimeCostComputation:
    def test_should_compute_travel_time_cost_when_no_travel(self) -> None:
        cost = compute_travel_time_cost(0 * minutes)

        assert cost == 0 * euros / yearly

    def test_should_compute_travel_time_cost(self) -> None:
        cost = compute_travel_time_cost(3600 * seconds)

        assert 3056 * euros / yearly <= cost <= 3057 * euros / yearly


class TestCarParkingCostComputation:
    def test_compute_car_parking_infrastructure_cost(
        self, cost_computer: CostComputer
    ) -> None:
        cost = cost_computer.compute_car_parking_infrastructure_cost()

        assert 760 * euros / yearly <= cost <= 761 * euros / yearly


class TestCarParkingCommuteCostComputation:
    def test_should_compute_car_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=500.0,
        )

        costs = cost_computer._compute_car_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_car_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=None,
        )
        costs = cost_computer._compute_car_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_car_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, cost_computer: CostComputer
    ) -> None:
        site = coworking_site()
        costs = cost_computer._compute_car_parking_commute_costs(site)

        assert costs == []


class TestRentOfficeCostComputation:
    def test_compute_office_cost_when_office_in_paris(self, geo_site_factory, address):
        paris = geo_site_factory(address_details=address(citycode="75102"))
        cost = compute_office_rent_cost(paris)

        assert 13582 * euros / yearly < cost < 13583 * euros / yearly

    def test_compute_office_cost_when_office_in_drome(self, geo_site_factory, address):
        drome = geo_site_factory(address_details=address(citycode="26xxx"))
        cost = compute_office_rent_cost(drome)

        assert 3855 * euros / yearly < cost < 3856 * euros / yearly

    def test_compute_office_cost_when_office_in_somewhere_very_far_away(
        self, geo_site_factory, address
    ):
        utopia_city = geo_site_factory(address_details=address(citycode="L@vE"))
        cost = compute_office_rent_cost(utopia_city)

        assert 2948 * euros / yearly < cost < 2949 * euros / yearly


class TestPTSubscriptionComputation:
    def test_compute_urban_pt_subscription_when_short_distance_traveled_inside_paris(
        self, address
    ):
        dede = address(citycode="75101")
        laser = address(citycode="75102")

        cost = compute_pt_subscription_cost(dede, laser, 3 * kilometers)

        assert 902 * euros / yearly <= cost <= 903 * euros / yearly

    def test_compute_urban_pt_subscription_when_short_distance_traveled_inside_marseille(
        self, address
    ):
        dede = address(citycode="13214")
        laser = address(citycode="13202")

        cost = compute_pt_subscription_cost(dede, laser, 3 * kilometers)

        assert 876 * euros / yearly <= cost <= 877 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled(
        self, address
    ):
        dede = address(citycode="xxxxx")
        laser = address(citycode="75102")

        cost = compute_pt_subscription_cost(dede, laser, 20 * kilometers)

        assert 1489 * euros / yearly <= cost <= 1490 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled_with_no_urban_network(
        self, address
    ):
        dede = address(citycode="Lolol")
        laser = address(citycode="wtf")

        cost = compute_pt_subscription_cost(dede, laser, 20 * kilometers)

        assert 587 * euros / yearly <= cost <= 588 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled_with_urban_network_at_destination(
        self, address
    ):
        dede = address(citycode="26333")
        laser = address(citycode="69386")

        cost = compute_pt_subscription_cost(dede, laser, 72 * kilometers)

        assert 2749 * euros / yearly <= cost <= 2750 * euros / yearly


class TestComputeActiveModesKmFee:
    def test_compute_null_walk_km_fee_for_null_distance(self):
        cost = compute_walk_km_fee(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_compute_null_bicycle_km_fee_for_null_distance(self):
        cost = compute_bicycle_km_fee(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_compute_some_walk_km_fee_for_non_null_distance(self):
        cost = compute_walk_km_fee(2 * kilometers)

        assert 69 * euros / yearly <= cost <= 70 * euros / yearly

    def test_compute_some_bicycle_km_fee_for_non_null_distance(self):
        cost = compute_bicycle_km_fee(10 * kilometers)

        assert 339 * euros / yearly <= cost <= 340 * euros / yearly


class TestComputeSustainableMobilityFee:
    def test_should_compute_positive_cost_for_employer(self):
        cost = compute_sustainable_mobility_fee_for_company()

        assert cost == 300 * euros / yearly

    def test_should_compute_negative_cost_for_employee(self):
        cost = compute_sustainable_mobility_fee_for_employee()

        assert cost == -300 * euros / yearly


class TestComputeBicycleParkingCost:
    def test_should_compute_parking_infrastructure_cost_for_bikes(
        self, cost_computer: CostComputer
    ) -> None:
        cost = cost_computer.compute_bicycle_parking_infrastructure_cost()

        assert 126 * euros / yearly <= cost <= 127 * euros / yearly


class TestBicycleParkingCommuteCostComputation:
    def test_should_compute_bicycle_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=500.0,
        )

        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_bicycle_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=None,
        )
        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, cost_computer: CostComputer
    ) -> None:
        site = coworking_site()
        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        assert costs == []


def assert_costs(costs: Costs, expected_costs: Costs):
    assert len(costs) == len(expected_costs), f"{costs} != {expected_costs}"
    for expected_cost in expected_costs:
        for actual_cost in costs:
            if (
                actual_cost.kind == expected_cost.kind
                and actual_cost.payer == expected_cost.payer
            ):
                print(expected_cost.kind)
                print(actual_cost.amount)
                print(expected_cost.amount)
                assert (
                    expected_cost.amount - 1 * euros / yearly
                    <= actual_cost.amount
                    <= expected_cost.amount + 1 * euros / yearly
                )
                break
        else:
            assert False, f"Missing expected cost {expected_cost}"


class TestCostComputation:
    def test_should_compute_no_costs_from_empty_commutes(
        self, cost_computer, individual_costs
    ):
        commutes = []

        costs = cost_computer.compute_commutes_costs(commutes)

        assert costs == individual_costs()

    def test_should_compute_costs_from_empty_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory(id=1)
        dede_commute = modal_commute_data
        valoche = geo_employee_factory(id=2)
        valoche_commute = modal_commute_data
        laser = geo_site_factory(address_details=address(citycode="75102"))
        commutes = [
            consolidated_commute_factory(dede, laser, dede_commute),
            consolidated_commute_factory(valoche, laser, valoche_commute),
        ]

        costs = cost_computer.compute_commutes_costs(commutes)

        assert set(costs.cost_by_employee.keys()) == {dede, valoche}
        assert len(costs.cost_by_employee.values()) == 2

    @pytest.mark.parametrize(
        "car_like_mode",
        [TransportMode.CAR, TransportMode.ELECTRIC_CAR, TransportMode.MOTORCYCLE],
    )
    def test_should_compute_all_costs_from_single_car_like_commutes(
        self,
        car_like_mode,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=car_like_mode,
            duration=ImmutableDict({car_like_mode: 3600}),
            distance=ImmutableDict({car_like_mode: 50000}),
            emission=ImmutableDict({car_like_mode: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_agnostic_carpooling_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CARPOOLING,
            duration=ImmutableDict({TransportMode.CARPOOLING: 3600}),
            distance=ImmutableDict({TransportMode.CARPOOLING: 50000}),
            emission=ImmutableDict({TransportMode.CARPOOLING: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / 2 / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_car_commutes_with_mobility_plan_benefits(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute, has_mobility_benefits=True)

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-788 * euros / yearly,
            ),
            cost(
                kind=CostKind.ABSENTEEISM,
                payer=CostPayer.COMPANY,
                amount=-267 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_pt_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.PUBLIC_TRANSPORT,
            duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 4000}),
            distance=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 60000}),
            emission=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 2100}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.COMPANY,
                amount=1270 * euros / yearly,
            ),
            cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.EMPLOYEE,
                amount=1270 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=5.09 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=197.165 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=121.20 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=0 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=56 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3395 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_walk_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="44102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.WALK,
            duration=ImmutableDict({TransportMode.WALK: 1600}),
            distance=ImmutableDict({TransportMode.WALK: 2000}),
            emission=ImmutableDict({TransportMode.WALK: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.WALK_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=69 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=-486 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=20 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=188 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=1358 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=3729 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    @pytest.mark.parametrize(
        "bike_like_mode", [TransportMode.BICYCLE, TransportMode.ELECTRIC_BICYCLE]
    )
    def test_should_compute_all_costs_from_single_bike_like_commutes(
        self,
        bike_like_mode,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="01053"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=bike_like_mode,
            duration=ImmutableDict({bike_like_mode: 2400}),
            distance=ImmutableDict({bike_like_mode: 10000}),
            emission=ImmutableDict({bike_like_mode: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commutes)

        expected_costs = (
            cost(
                kind=CostKind.BIKE_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=339 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=300 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=-300 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=-2431 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=94 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=1081 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=2037 * euros / yearly,
            ),
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=126 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=3452 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_carpooler_driver_commute(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3000}),
            distance=ImmutableDict({TransportMode.CAR: 48000}),
            emission=ImmutableDict({TransportMode.CAR: 9000}),
            alternative_arrival_time=ImmutableDict(),
        )
        dede_carpools_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        groupe_commute = consolidated_commute_factory(
            dede, laser, dede_carpools_to_the_laser
        )
        ride_share_factor = 0.6

        costs = cost_computer.compute_cost_for_carpooling_employee(
            groupe_commute, commutes, ride_share_factor
        )

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-787 * euros / yearly,
            ),
            cost(
                kind=CostKind.ABSENTEEISM,
                payer=CostPayer.COMPANY,
                amount=-267 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=45 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=451 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=2546 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=300 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=-300 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)


class TestComputeCostAdjustmentsForRemoteCommutes:
    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_not_adjust_costs_for_non_trip_related_costs(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-120 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-120 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_trip_related_costs(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=4 / 5 * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_parking_spots_if_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        commutes = [commute]
        for employee_index in range(1, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index),
                    laser,
                    dede_goes_to_the_laser,
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_parking_spots_saved = 1 * adimensional
        nb_remote_workers = 6 * adimensional
        cost_factor = 1 * adimensional - (nb_parking_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_parking_spots_saved = 1 * adimensional
        nb_remote_workers = 6 * adimensional
        cost_factor = 1 * adimensional - (nb_parking_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_not_adjust_rent_costs_if_not_enough_workers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        commutes = [commute]

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_not_adjust_rent_costs_if_workers_are_split_between_sites(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0, address_details=address(citycode="75102"))
        macoumba = geo_site_factory(id=1, address_details=address(citycode="63170"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 3):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )
        for employee_index in range(3, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), macoumba, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.costs.CostComputer.compute_commute_cost")
    def test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0, address_details=address(citycode="75102"))
        macoumba = geo_site_factory(id=1, address_details=address(citycode="63170"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 32):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )
        for employee_index in range(30, 32):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), macoumba, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_office_spots_saved = (32 // 5) * adimensional
        nb_remote_workers = 32 * adimensional
        cost_factor = 1 * adimensional - (nb_office_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs


class TestCarCostsComputation:
    def test_should_compute_car_costs_to_geo_site(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
    ):
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0)
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 360}),
            distance=ImmutableDict({TransportMode.CAR: 5000}),
            emission=ImmutableDict({TransportMode.CAR: 2000}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)

        costs = cost_computer._compute_car_like_costs(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1920 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=6.91 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=406 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=1003 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=115.43 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=4 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=305 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=2948 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_car_costs_to_coworking_site(
        self,
        cost_computer,
        geo_employee_factory,
        coworking_site,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
    ):
        dede = geo_employee_factory(id=0)
        la_fabrik = coworking_site()
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 360}),
            distance=ImmutableDict({TransportMode.CAR: 5000}),
            emission=ImmutableDict({TransportMode.CAR: 2000}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, la_fabrik, commute_data)

        costs = cost_computer._compute_car_like_costs(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1920 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=6.91 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=406 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=1003 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=115.43 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=4 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=305 * euros / yearly,
            ),
            cost(
                kind=CostKind.COWORKING_RENT,
                payer=CostPayer.COMPANY,
                amount=6000 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)


class TestComputeHealthInsuranceCosts:
    def test_should_compute_insurance_cost_with_no_benefits(self, cost_computer, cost):
        costs = cost_computer.compute_health_insurance_costs(10, 0)

        expected_costs = (
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.COMPANY,
                amount=8133 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.EMPLOYEE,
                amount=4781 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_insurance_cost_with_benefits(self, cost_computer, cost):
        costs = cost_computer.compute_health_insurance_costs(10, 5)

        expected_costs = (
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.COMPANY,
                amount=7729 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.EMPLOYEE,
                amount=4545 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)
