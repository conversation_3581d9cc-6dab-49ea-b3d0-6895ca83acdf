import networkx
import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.internal.travel_time import InternalTravelTimer
from internal_api.geo_graph_from_json import make_geo_graph
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode, TransportType
from mobility.quantity import gramEC, kilometers, meters, seconds
from mobility.workers.distance_computer import compute_distance


@pytest.mark.parametrize(
    "mode,emission",
    [
        (TransportMode.CAR, 192 * gramEC / kilometers),
        (TransportMode.MOTORCYCLE, 165 * gramEC / kilometers),
        (TransportMode.CARPOOLING, 96 * gramEC / kilometers),
        (TransportMode.ELECTRIC_CAR, 19.8 * gramEC / kilometers),
    ],
)
class TestInternalTravelTimerCarJourneys:
    def test_should_compute_car_journey_in_one_edge_graph_no_congestion(
        self, mode, emission
    ):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=10.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, mode, (8, 30))

        assert journey == JourneyAttribute(
            duration=int(10),
            emission=int((1.0 * meters * emission) / gramEC),
            distance=1,
        )

    def test_should_compute_car_journey_in_one_edge_graph_with_congestion(
        self, mode, emission
    ):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=2.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, mode, (8, 30))

        assert journey == JourneyAttribute(
            duration=2,
            emission=int((1.0 * meters * emission) / gramEC),
            distance=1,
        )

    def test_should_compute_car_journey_in_one_edge_graph_no_congestion_with_connectors(
        self, mode, emission
    ):
        graph = networkx.DiGraph()
        zerorigin = GeoCoordinates(0.99, 0.99)
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        thrination = GeoCoordinates(2.01, 2.01)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=100.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(zerorigin, thrination, mode, (8, 30))

        connection_distance = compute_distance(zerorigin, origin) + compute_distance(
            destination, thrination
        )
        distance = 100.0 * meters + connection_distance
        duration = (
            100.0 * seconds + connection_distance / travel_timer.CONNECTOR_CAR_SPEED
        )
        emission = distance * emission
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == int(emission / gramEC)

    def test_should_compute_car_journey_in_one_edge_graph_with_congestion_and_connectors(
        self, mode, emission
    ):
        graph = networkx.DiGraph()
        zerorigin = GeoCoordinates(0.99, 0.99)
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        thrination = GeoCoordinates(2.01, 2.01)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=100.0, duration_congestion=200.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(zerorigin, thrination, mode, (8, 30))

        connection_distance = compute_distance(zerorigin, origin) + compute_distance(
            destination, thrination
        )
        distance = 100.0 * meters + connection_distance
        duration = (
            200.0 * seconds + connection_distance / travel_timer.CONNECTOR_CAR_SPEED
        )
        emission = distance * emission
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == int(emission / gramEC)

    def test_should_infer_congestion_based_on_edges_around(self, mode, emission):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=2.0, distance=1.0)
        graph.add_edge(2, 3, duration=1.0, distance=1.0)
        graph.add_edge(3, 4, duration=1.0, duration_congestion=1.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, mode, (8, 30))

        assert journey == JourneyAttribute(
            duration=int(2.0 + 1.5 + 1.0),
            emission=int((3.0 * meters * emission) / gramEC),
            distance=3,
        )

    def test_should_choose_shortest_congested_path(self, mode, emission):
        """
        Graph: <F: fast> <S: slow> <HC/LC: high/low congestion>
                 ,--> [2] ----(F,?)----> [3] --,
                /                               |
             (F,HC)                           (F,HC)
              /                                 ',
            [1]                                   )-> [4]
             |                                   /
           (S,LC)                             (S,LC)
             |                                 /
              `-----> [5] ----(S,?)----> [6] -'
        """
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_node(5, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(6, coordinates=middle_coords, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=10.0, distance=1.0)
        graph.add_edge(2, 3, duration=1.0, duration_congestion=10.0, distance=1.0)
        graph.add_edge(3, 4, duration=1.0, duration_congestion=10.0, distance=1.0)
        graph.add_edge(1, 5, duration=2.0, duration_congestion=4.0, distance=10.0)
        graph.add_edge(5, 6, duration=3.0, duration_congestion=6.0, distance=10.0)
        graph.add_edge(6, 4, duration=2.0, duration_congestion=4.0, distance=10.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, mode, (8, 30))

        assert journey == JourneyAttribute(
            duration=14,
            emission=int((30.0 * meters * emission) / gramEC),
            distance=30,
        )

    def test_should_not_path_through_pt_edges(self, mode, emission):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=10.0)
        graph.add_edge(
            2,
            3,
            duration=100.0,
            distance=1000.0,
            transport_type=TransportType.BUS,
        )
        graph.add_edge(3, 4, duration=1.0, distance=10.0)
        graph.add_edge(1, 4, duration=100.0, duration_congestion=100.0, distance=1000.0)
        graph.add_edge(4, 1, duration=100.0, duration_congestion=100.0, distance=1000.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, mode, (8, 30))

        emission = 1000.0 * meters * emission
        assert journey == JourneyAttribute(
            duration=100,
            emission=int(emission / gramEC),
            distance=1000,
        )


class TestInternalTravelTimerPublicTransportJourneys:
    def test_should_path_through_pt_edges(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=10.0)
        graph.add_edge(
            2,
            3,
            duration=100.0,
            distance=1000.0,
            transport_type=TransportType.BUS,
        )
        graph.add_edge(3, 4, duration=1.0, distance=10.0)
        graph.add_edge(1, 4, duration=100.0, duration_congestion=100.0, distance=1000.0)
        graph.add_edge(4, 1, duration=100.0, duration_congestion=100.0, distance=1000.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            origin, destination, TransportMode.PUBLIC_TRANSPORT, (8, 30)
        )

        bus_emissions = 103 * gramEC / kilometers
        emission = 1000.0 * meters * bus_emissions
        assert journey == JourneyAttribute(
            duration=201,
            emission=int(emission / gramEC),
            distance=1020,
        )


class TestInternalTravelTimerWalkJourneys:
    def test_should_compute_walk_journey_in_one_edge_graph_no_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.WALK, (8, 30))

        distance = 1.0 * meters
        duration = distance / travel_timer.WALK_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=int(distance / meters),
        )

    def test_should_compute_walk_journey_in_one_edge_graph_with_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=2.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.WALK, (8, 30))

        distance = 1.0 * meters
        duration = distance / travel_timer.WALK_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=int(distance / meters),
        )

    def test_should_compute_walk_journey_in_one_edge_graph_no_congestion_with_connectors(
        self,
    ):
        graph = networkx.DiGraph()
        zerorigin = GeoCoordinates(0.99, 0.99)
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        thrination = GeoCoordinates(2.01, 2.01)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=100.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(zerorigin, thrination, TransportMode.WALK, (8, 30))

        connection_distance = compute_distance(zerorigin, origin) + compute_distance(
            destination, thrination
        )
        distance = 100.0 * meters + connection_distance
        duration = distance / travel_timer.WALK_SPEED
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == 0

    def test_should_choose_shortest_distance_path(self):
        """
        Graph: <F: fast> <S: slow> <HC/LC: high/low congestion>
                 ,--> [2] ----(F,?)----> [3] --,
                /                               |
             (F,HC)                           (F,HC)
              /                                 ',
            [1]                                   )-> [4]
             |                                   /
           (S,LC)                             (S,LC)
             |                                 /
              `-----> [5] ----(S,?)----> [6] -'
        """
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_node(5, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(6, coordinates=middle_coords, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(2, 3, duration=1.0, distance=10.0)
        graph.add_edge(3, 4, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(1, 5, duration=2.0, duration_congestion=4.0, distance=1.0)
        graph.add_edge(5, 6, duration=3.0, distance=1.0)
        graph.add_edge(6, 4, duration=2.0, duration_congestion=4.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.WALK, (8, 30))

        distance = 3.0 * meters
        duration = distance / travel_timer.WALK_SPEED
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == 0

    def test_should_not_path_through_pt_edges(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=10.0)
        graph.add_edge(
            2,
            3,
            duration=100.0,
            distance=1000.0,
            transport_type=TransportType.BUS,
        )
        graph.add_edge(3, 4, duration=1.0, distance=10.0)
        graph.add_edge(1, 4, duration=100.0, duration_congestion=100.0, distance=1000.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.WALK, (8, 30))

        duration = 1000.0 * meters / travel_timer.WALK_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=1000,
        )


class TestInternalTravelTimerBicycleJourneys:
    def test_should_compute_bicycle_journey_in_one_edge_graph_no_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.BICYCLE, (8, 30))

        distance = 1.0 * meters
        duration = distance / travel_timer.BICYCLE_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=int(distance / meters),
        )

    def test_should_compute_bicycle_journey_in_one_edge_graph_with_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=2.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.BICYCLE, (8, 30))

        distance = 1.0 * meters
        duration = distance / travel_timer.BICYCLE_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=int(distance / meters),
        )

    def test_should_compute_bicycle_journey_in_one_edge_graph_no_congestion_with_connectors(
        self,
    ):
        graph = networkx.DiGraph()
        zerorigin = GeoCoordinates(0.99, 0.99)
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        thrination = GeoCoordinates(2.01, 2.01)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=100.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            zerorigin, thrination, TransportMode.BICYCLE, (8, 30)
        )

        connection_distance = compute_distance(zerorigin, origin) + compute_distance(
            destination, thrination
        )
        distance = 100.0 * meters + connection_distance
        duration = distance / travel_timer.BICYCLE_SPEED
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == 0

    def test_should_choose_shortest_distance_path(self):
        """
        Graph: <F: fast> <S: slow> <HC/LC: high/low congestion>
                 ,--> [2] ----(F,?)----> [3] --,
                /                               |
             (F,HC)                           (F,HC)
              /                                 ',
            [1]                                   )-> [4]
             |                                   /
           (S,LC)                             (S,LC)
             |                                 /
              `-----> [5] ----(S,?)----> [6] -'
        """
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_node(5, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(6, coordinates=middle_coords, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(2, 3, duration=1.0, distance=10.0)
        graph.add_edge(3, 4, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(1, 5, duration=2.0, duration_congestion=4.0, distance=1.0)
        graph.add_edge(5, 6, duration=3.0, distance=1.0)
        graph.add_edge(6, 4, duration=2.0, duration_congestion=4.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.BICYCLE, (8, 30))

        distance = 3.0 * meters
        duration = distance / travel_timer.BICYCLE_SPEED
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == 0

    def test_should_not_path_through_pt_edges(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=10.0)
        graph.add_edge(
            2,
            3,
            duration=100.0,
            distance=1000.0,
            transport_type=TransportType.BUS,
        )
        graph.add_edge(3, 4, duration=1.0, distance=10.0)
        graph.add_edge(1, 4, duration=100.0, duration_congestion=100.0, distance=1000.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(origin, destination, TransportMode.BICYCLE, (8, 30))

        duration = 1000.0 * meters / travel_timer.BICYCLE_SPEED
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=0,
            distance=1000,
        )


class TestInternalTravelTimerElectricBicycleJourneys:
    def test_should_compute_bicycle_journey_in_one_edge_graph_no_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            origin, destination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
        )

        distance = 1.0 * meters
        duration = distance / travel_timer.ELECTRIC_BICYCLE_SPEED
        emission = distance * travel_timer.ELECTRIC_BICYCLE_EMISSION
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=int(emission / gramEC),
            distance=int(distance / meters),
        )

    def test_should_compute_bicycle_journey_in_one_edge_graph_with_congestion(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=2.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            origin, destination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
        )

        distance = 1.0 * meters
        duration = distance / travel_timer.ELECTRIC_BICYCLE_SPEED
        emission = distance * travel_timer.ELECTRIC_BICYCLE_EMISSION
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=int(emission / gramEC),
            distance=int(distance / meters),
        )

    def test_should_compute_bicycle_journey_in_one_edge_graph_no_congestion_with_connectors(
        self,
    ):
        graph = networkx.DiGraph()
        zerorigin = GeoCoordinates(0.99, 0.99)
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        thrination = GeoCoordinates(2.01, 2.01)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=100.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            zerorigin, thrination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
        )

        connection_distance = compute_distance(zerorigin, origin) + compute_distance(
            destination, thrination
        )
        distance = 100.0 * meters + connection_distance
        duration = distance / travel_timer.ELECTRIC_BICYCLE_SPEED
        emission = distance * travel_timer.ELECTRIC_BICYCLE_EMISSION
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == int(emission / gramEC)

    def test_should_choose_shortest_distance_path(self):
        """
        Graph: <F: fast> <S: slow> <HC/LC: high/low congestion>
                 ,--> [2] ----(F,?)----> [3] --,
                /                               |
             (F,HC)                           (F,HC)
              /                                 ',
            [1]                                   )-> [4]
             |                                   /
           (S,LC)                             (S,LC)
             |                                 /
              `-----> [5] ----(S,?)----> [6] -'
        """
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_node(5, coordinates=middle_coords, merged_from="car_graph")
        graph.add_node(6, coordinates=middle_coords, merged_from="car_graph")
        graph.add_edge(1, 2, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(2, 3, duration=1.0, distance=10.0)
        graph.add_edge(3, 4, duration=1.0, duration_congestion=10.0, distance=10.0)
        graph.add_edge(1, 5, duration=2.0, duration_congestion=4.0, distance=1.0)
        graph.add_edge(5, 6, duration=3.0, distance=1.0)
        graph.add_edge(6, 4, duration=2.0, duration_congestion=4.0, distance=1.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            origin, destination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
        )

        distance = 3.0 * meters
        duration = distance / travel_timer.ELECTRIC_BICYCLE_SPEED
        emission = distance * travel_timer.ELECTRIC_BICYCLE_EMISSION
        assert journey.duration == int(duration / seconds)
        assert journey.distance == int(distance / meters)
        assert journey.emission == int(emission / gramEC)

    def test_should_not_path_through_pt_edges(self):
        graph = networkx.DiGraph()
        origin = GeoCoordinates(1.0, 1.0)
        destination = GeoCoordinates(2.0, 2.0)
        middle_coords = GeoCoordinates(1.5, 1.5)
        graph.add_node(1, coordinates=origin, merged_from="car_graph")
        graph.add_node(2, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(3, coordinates=middle_coords, merged_from="pt_graph")
        graph.add_node(4, coordinates=destination, merged_from="car_graph")
        graph.add_edge(1, 2, duration=100.0, distance=10.0)
        graph.add_edge(
            2,
            3,
            duration=100.0,
            distance=1000.0,
            transport_type=TransportType.BUS,
        )
        graph.add_edge(3, 4, duration=1.0, distance=10.0)
        graph.add_edge(1, 4, duration=100.0, duration_congestion=100.0, distance=1000.0)
        geo_graph = make_geo_graph(graph)
        travel_timer = InternalTravelTimer("", EventReporter(), geo_graph)

        journey = travel_timer.time(
            origin, destination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
        )

        distance = 1000.0 * meters
        duration = distance / travel_timer.ELECTRIC_BICYCLE_SPEED
        emission = distance * travel_timer.ELECTRIC_BICYCLE_EMISSION
        assert journey == JourneyAttribute(
            duration=int(duration / seconds),
            emission=int(emission / gramEC),
            distance=1000,
        )
