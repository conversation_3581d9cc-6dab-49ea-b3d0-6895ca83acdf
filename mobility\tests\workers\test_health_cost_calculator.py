from typing import Callable

import pytest

from mobility.ir.cost import <PERSON><PERSON><PERSON><PERSON>, CostPayer, Costs
from mobility.ir.transport import TransportMode
from mobility.quantity import euros, kilometers, yearly
from mobility.workers.health_cost_calculator import HealthCostCalculator


def assert_costs(costs: Costs, expected_costs: Costs):
    assert len(costs) == len(expected_costs), f"{costs} != {expected_costs}"
    for expected_cost in expected_costs:
        for actual_cost in costs:
            if (
                actual_cost.kind == expected_cost.kind
                and actual_cost.payer == expected_cost.payer
            ):
                print(expected_cost.kind)
                print(actual_cost.amount)
                print(expected_cost.amount)
                assert (
                    expected_cost.amount - 1 * euros / yearly
                    <= actual_cost.amount
                    <= expected_cost.amount + 1 * euros / yearly
                )
                break
        else:
            assert False, f"Missing expected cost {expected_cost}"


@pytest.fixture
def calculator() -> HealthCostCalculator:
    return HealthCostCalculator()


class TestHealthImpactComputation:
    def test_should_compute_bc_health_impact_cost_when_no_travel(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.BICYCLE, 0 * kilometers
        )

        assert len(cost) == 1
        assert cost[0].amount == 0 * euros / yearly

    def test_should_compute_map_health_impact_cost_when_no_travel(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.WALK, 0 * kilometers
        )

        assert len(cost) == 1
        assert cost[0].amount == 0 * euros / yearly

    def test_should_compute_bc_health_impact_cost(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.BICYCLE, 10 * kilometers
        )

        assert len(cost) == 1
        assert -2432 * euros / yearly < cost[0].amount < -2431 * euros / yearly

    def test_should_compute_map_health_impact_cost(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.WALK, 10 * kilometers
        )

        assert len(cost) == 1
        assert -2432 * euros / yearly < cost[0].amount < -2431 * euros / yearly

    def test_should_compute_car_health_impact_cost(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.CAR, 10 * kilometers
        )

        assert cost == []

    def test_should_compute_pt_health_impact_cost(
        self, calculator: HealthCostCalculator
    ) -> None:
        cost = calculator._compute_health_cost_for_society(
            TransportMode.PUBLIC_TRANSPORT, 10 * kilometers
        )

        assert cost == []


class TestComputeHealthInsuranceCosts:

    def test_should_compute_insurance_cost_with_no_benefits(self, calculator: HealthCostCalculator, cost: Callable):
        costs = calculator.compute_health_insurance_costs(10, 0)

        expected_costs = (
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.COMPANY,
                amount=8133 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.EMPLOYEE,
                amount=4781 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_insurance_cost_with_benefits(self, calculator: HealthCostCalculator, cost: Callable):
        costs = calculator.compute_health_insurance_costs(10, 5)

        expected_costs = (
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.COMPANY,
                amount=7729 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_INSURANCE,
                payer=CostPayer.EMPLOYEE,
                amount=4545 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)