{"api_abstraction/geopy/test_herev7.py": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/charter.scenario.poi_scenario.site/poi_isochrone_map_and_poi_times_to_site.jinja-template_parameters6]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/charter.scenario.poi_scenario.transport_modes/poi_access_times_multi_site.jinja-template_parameters7]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/charter.scenario.pt_indicators.transport_types.transport_mode.site/pt_infrastructure_map_and_access_time.jinja-template_parameters8]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/charter.scenario.transport_modes/yearly_carbon_emissions_pt_car.jinja-template_parameters9]": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestStudyGeopackageSerializer::test_study_geopackage_serializer_serialize": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestStudyGeopackageSerializer::test_study_geopackage_serializer_write_geopackage": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_geopackage_study_init": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_geopackage_study_sites": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_geopackage_study_commutes": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_geopackage_study_isochrones": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_geopackage_study_convert_polygons_to_multipolygon_if_needed": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_extract_study_data_empty": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_extract_infrastucture_data": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_construct_bicycle_amenities": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_get_bike_shift_indicators": true, "mobility/tests/ir/indicators/test_indicators.py::TestGetScenarioIndicator::test_should_raise_error_if_scenario_not_in_study": true, "mobility/tests/ir/test_country.py::TestCountryData::test_country_data_initializes_with_bounding_box_from_territories": true, "mobility/tests/ir/test_country.py::TestCountryData::test_get_territory_returns_correct_territory": true, "mobility/tests/ir/test_country.py::TestCountryData::test_get_territory_raises_value_error_when_territory_not_found": true, "mobility/tests/ir/test_country.py::TestCountryData::test_add_territory_adds_territory_to_list": true, "mobility/tests/ir/test_country.py::TestCountry::test_demonym_should_return_str_for_every_country[country0]": true, "mobility/tests/ir/test_country.py::TestCountry::test_demonym_should_return_str_for_every_country[country1]": true, "mobility/tests/ir/test_country.py::TestCountry::test_demonym_should_return_str_for_every_country[country2]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.costs.indicators\\\\mobility_costs_account.jinja-template_parameters0]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.indicators.illustrator\\\\study_characteristics_diag.jinja-template_parameters1]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.poi_scenario.sites.transport_modes\\\\poi_access_time_table.jinja-template_parameters2]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario\\\\score_card_and_map.jinja-template_parameters3]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.bicycle_indicators.amenity_types.transport_mode.site\\\\bicycle_infrastructure_map_and_access_time.jinja-template_parameters4]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.car_indicators.amenity_types.transport_mode.site\\\\car_infrastructure_map_and_access_time.jinja-template_parameters5]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.indicators.transport_modes\\\\yearly_carbon_emissions_pt_car.jinja-template_parameters6]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.poi_scenario.site\\\\poi_isochrone_map_and_poi_times_to_site.jinja-template_parameters7]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.poi_scenario.transport_modes\\\\poi_access_times_multi_site.jinja-template_parameters8]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.pt_indicators.transport_types.transport_mode.site\\\\pt_infrastructure_map_and_access_time.jinja-template_parameters9]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.study_indicators.accessibility_slices.scenario\\\\scenario_access_times_summary.jinja-template_parameters10]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.study_indicators.scenario.transport_modes\\\\modal_share_and_modal_transport_time.jinja-template_parameters11]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.study_indicators.scenario.transport_modes\\\\modes_inference_details.jinja-template_parameters12]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\illustrator.indicators\\\\scenarios_workforce_description.jinja-template_parameters13]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\illustrator.scenario\\\\sites_illustration_table.jinja-template_parameters14]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\indicators\\\\study_characteristics_loc_study.jinja-template_parameters15]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\modus_operandi_diag.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\modus_operandi_loc_study.jinja-template_parameters17]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\poi_scenario.sites.transport_modes\\\\car_pt_closest_poi_description.jinja-template_parameters18]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\valorisation_score_description.jinja-template_parameters19]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/charter.scenario.indicators.transport_modes/yearly_carbon_emissions_pt_car.jinja-template_parameters6]": true, "mobility/tests/serializers/test_geopackage_serializer.py::TestGeopackageStudy::test_contruct_bicycle_lines": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value25-0-1235m]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value25-0-1254km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value26-1-1254.7km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value26-2-1254.7km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value26-3-1254.7km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value25-0-1000km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value26-3-1255km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value25-0-1 000km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value26-3-1 255km]": true, "mobility/tests/test_quantity.py::TestSIQuantities::test_can_print_quantities_rounded[value25-0-1\\xa0000km]": true, "mobility/tests/ir/indicators/test_pt_infrastructure_indicators.py::TestPtInfrastructureIndicators::test_should_include_stops_with_empty_lines_kinds_if_duration_is_within_min_max_duration": true, "mobility/tests/ir/indicators/test_pt_infrastructure_indicators.py::TestPtInfrastructureIndicators::test_should_include_stops_with_lines_kinds_if_duration_is_within_max_duration": true, "mobility/tests/ir/indicators/test_pt_infrastructure_indicators.py::TestPtInfrastructureIndicators::test_should_not_include_stops_with_lines_kinds_if_duration_exceeds_max_duration": true, "mobility/tests/ir/indicators/test_pt_infrastructure_indicators.py::TestStopFilter::test_should_not_include_stops_with_lines_kinds_if_max_duration_is_not_defined": true, "mobility/tests/ir/indicators/test_pt_infrastructure_indicators.py::TestStopFilter::test_filter_stops_should_raise_if_max_duration_is_not_defined": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_should_map_with_basemap_error": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_add_layers_for_clouds": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_add_layers_for_geometries": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_add_layers_for_multilines": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_create_geometries_feature_set": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::test_add_entry": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::test_to_svg": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestLegendPosition::test_legend_position_invalid": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::test_legend_position_invalid": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_create_symbol_node_circle": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_create_symbol_node_rect": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_create_symbol_node_star": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_create_symbol_node_invalid_shape": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_add_entry": true, "mobility/tests/serializers/charters/test_svg_map_addons.py::TestSVGMapLegend::test_to_svg": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_calculate_margins": true, "mobility/tests/ir/indicators/test_scenario_indicators.py::TestComputeNbEmployeesInIntervalByForcedMode::test_get_top_cities_with_most_employees": true, "mobility/tests/ir/indicators/test_scenario_indicators.py::TestGetCitiesWithMostEmployees::test_get_sites_by_commune_single_site": true, "mobility/tests/ir/indicators/test_scenario_indicators.py::TestGetCitiesWithMostEmployees::test_get_sites_by_commune_multiple_sites_same_city": true, "mobility/tests/ir/indicators/test_scenario_indicators.py::TestGetCitiesWithMostEmployees::test_get_sites_by_commune_multiple_sites_different_cities": true, "mobility/tests/ir/indicators/test_scenario_indicators.py::TestGetCitiesWithMostEmployees::test_get_sites_by_commune_no_sites": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_fail_on_invalid_arrival_time[8:30]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_fail_on_invalid_arrival_time[08:30]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_parse_arrival_time[8:30:0-expected_time1]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_parse_arrival_time[08:30:00-expected_time2]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_parse_arrival_time[08:30:00-expected_time3]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/modus_operandi_diag.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/modus_operandi_loc_study.jinja-template_parameters17]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_fail_on_invalid_poi_format": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_fail_on_invalid_poi_extra_values": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_fail_on_invalid_poi_missing_address": true, "mobility/tests/workers/test_territory_computer.py::TestAddTransportInfrastructure::test_should_create_poi_scenarios": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/modus_operandi.indicators/modus_operandi_diag.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/modus_operandi.indicators/modus_operandi_loc_study.jinja-template_parameters17]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/indicators.modus_operandi/modus_operandi_diag.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/indicators.modus_operandi/modus_operandi_loc_study.jinja-template_parameters17]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/indicators.modus_operandi_diag/modus_operandi_diag.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content/indicators.modus_operandi_diag/modus_operandi_loc_study.jinja-template_parameters17]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_should_populate_study_data": true, "mobility/tests/workers/test_compute_poi_scenario.py::test_should_return_poi_scenarios": true, "mobility/tests/serializers/test_arcgis_map_maker.py::TestArcGisMapMaker::test_add_legend_to_map_with_railway_lines": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_compute_route_for_car": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_compute_route_for_bicycle": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_invalid_status_response": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_wrong_row_count": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_wrong_elements_count": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_wrong_element_status": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_missing_duration_in_traffic": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_invalid_transport_mode": true, "mobility/tests/workers/test_transport_mode_computer.py::TestRuleBasedTransportModeAssigner::test_apply_forced_modes_by_country": true, "mobility/tests/workers/test_transport_mode_computer.py::TestRuleBasedTransportModeAssigner::test_apply_distance_constraints_for_intercontinental": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\employee_id_to_coworking_site_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\employee_id_to_coworking_site_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_modal_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_modal_commute_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_no_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_poi_timed_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_poi_timed_commute_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_bike_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_bike_commute_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_bike_commute_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_car_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_car_commute_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_car_commute_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_pt_stop_commute_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_pt_stop_commute_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_pt_stop_commute_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\base_commute_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\base_commute_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\modal_commute_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\modal_commute_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\timed_commute_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\timed_commute_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\time_indexed_base_commute_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\time_indexed_base_commute_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\crit_air\\\\crit_air_category_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\employees_mapping_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\employees_mapping_0_1.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\failed_geo_employee_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\failed_geo_employee_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\geo_employee_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\geo_employee_0_1.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\geo_employee_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\error\\\\error_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\address_details_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\geo_coordinates_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\multi_polygon_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\polygon_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\amenity_type_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\bike_amenity_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\bike_line_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\bike_line_0_1.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\car_amenity_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\car_way_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\pt_line_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\pt_stop_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\isochrone\\\\site_mode_isochrone_mapping_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\coworking_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\coworking_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_modal_commute_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_modal_commute_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_no_commute_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_poi_commute_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_poi_commute_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_bike_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_bike_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_bike_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_car_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_car_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_car_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_pt_stop_scenario_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_pt_stop_scenario_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_pt_stop_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario_data\\\\scenario_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario_data\\\\scenario_data_mapping_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\coworking_site_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\failed_geo_site_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\geo_site_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\sites_mapping_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\any_consolidated_study.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_10_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_10_1.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_10_2.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_11_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_12_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_13_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_4_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_5_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_6_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_7_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_8_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_9_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\study_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\study_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\study_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\territory\\\\point_of_interest_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\modal_data_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\modal_data_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_mode_0_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_mode_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_type_0_0.json]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\charter.scenario.indicators.enums.transport_modes\\\\yearly_carbon_emissions_pt_car.jinja-template_parameters6]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\indicators\\\\modus_operandi_diag.jinja-template_parameters15]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\indicators\\\\modus_operandi_loc_study.jinja-template_parameters16]": true, "mobility/tests/template/content/test_content_generation.py::TestContentTemplatesGenerateValidHTML::test_all_templates_generate_valid_html[content\\\\indicators\\\\study_characteristics_loc_study.jinja-template_parameters17]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\base_commute_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\modal_commute_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\timed_commute_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\modal_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_mode_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\employee_id_to_coworking_site_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_modal_commute_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_poi_timed_commute_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_bike_commute_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_car_commute_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_pt_stop_commute_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\time_indexed_base_commute_data_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\failed_geo_employee_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\geo_employee_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\coworking_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_modal_commute_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_poi_commute_scenario_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_bike_scenario_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_car_scenario_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_pt_stop_scenario_3_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study_14_0.json]": true, "mobility/tests/builders/test_airport_database.py::TestAirportDatabase::test_find_nearest_airport_without_loading": true, "mobility/tests/builders/test_airport_database.py::TestAirportDatabase::test_get_airport_without_loading": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_missing_mode_exception_message_explicitly_states_the_issue": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_not_ok_status_error_message_is_explicit": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_wrong_row_count_error_message_is_explicit": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_wrong_elements_count_error_message_is_explicit": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_wrong_elements_status_error_message_is_explicit": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_no_duration_in_traffic_error_message_is_explicit": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_compute_route_is_resilient_to_transient_traffic_failure": true, "api_abstraction/tests/google/test_distance_matrix_api.py::TestDistanceMatrix::test_compute_route_times_out_on_repeated_traffic_failure": true, "api_abstraction/tests/google/test_distance_api.py::TestDistanceMatrixApi::test_time_modes_similar_to_driving[MOTORCYCLE]": true, "mobility/tests/entry_points/test_run_carbon_computer.py::TestRunCarbonComputer::test_should_return_zero_on_launch_from_study_json_file_and_reassign_modes_without_recompute": true, "tests/scripts/test_calculate_inside_buffers_count.py::test_calculate_population_count_with_point_stop_stations": true, "tests/scripts/test_calculate_inside_buffers_count.py::test_create_buffered_stations": true, "tests/scripts/test_calculate_inside_buffers_count.py::test_calculate_population_within_buffers_attribute_grouping": true, "tests/api_abstraction/here/test_api.py::TestHereTravelTimeAPI": true, "tests/api_abstraction/here/test_here_api_clients.py::TestHereRoutingAPI": true, "tests/api_abstraction/here/test_here_api_clients.py::TestHerePublicTransitAPI": true, "tests/api_abstraction/here/test_here_api_clients.py::TestHereIntermodalAPI": true, "tests/api_abstraction/here/test_here_api_clients.py::TestHereIsolineAPI": true, "tests/api_abstraction/here/test_here_api_clients.py::TestConvertHereResultToJourneyAttribute": true, "tests/api_abstraction/here/test_api.py::TestHereTravelTimeAPI::test_compute_isochrone_exception": true, "tests/api_abstraction/here/test_here_api_clients.py::TestHereIsolineAPI::test_format_result_success": true, "api_abstraction/tests/here/test_base_here_api.py::TestBaseHereAPI::test_abstract_methods": true, "api_abstraction/tests/here/test_base_here_api.py::TestBaseHereAPI::test_maps_transport_modes_correctly[ELECTRIC_BICYCLE-bicycle]": true, "api_abstraction/tests/here/test_here_api_clients.py::TestHereIsolineAPI::test_format_result_success": true, "api_abstraction/tests/here/test_here_api_clients.py::TestHereIsolineAPI::test_format_result_should_return_shape": true, "api_abstraction/tests/here/test_here_api_clients.py::TestHereIntermodalAPI::test_format_input_parameters_prioritizes_exclude_over_include_vehicle_modes": true, "api_abstraction/tests/here/test_here_api_clients.py::TestHereIntermodalAPI::test_format_input_parameters_prioritizes_exclude_over_include_transit_modes": true, "api_abstraction/tests/trivial/test_trivial_airplane_timer.py::TestTrivialAirplaneTimer::test_time_airplane_mode": true, "api_abstraction/tests/trivial/test_trivial_airplane_timer.py::TestTrivialAirplaneTimer::test_time_non_airplane_mode_raises_error": true, "api_abstraction/tests/trivial/test_trivial_airplane_timer.py::TestTrivialAirplaneTimer::test_compute_isochrone_not_implemented": true, "api_abstraction/tests/trivial/test_trivial_airplane_timer.py::TestTrivialAirplaneTimer::test_compute_detours_not_implemented": true, "api_abstraction/tests/trivial/test_trivial_airplane_timer.py::TestTrivialFlightComputerApiWithNoneEmission::test_combine_journey_segments_with_none_emission": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\employees_mapping_1_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\employees_mapping_2_0.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\consolidated_study.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\modal_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_mode.json]": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v13_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v1_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v2_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v3_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v4_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v5_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v6_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v7_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v8_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v9_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v10_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v10_1_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v10_2_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v11_0_json": true, "mobility/tests/json_schemas/test_versioning.py::TestJsonSchemaVersioning::test_schema_without_version_validates_v12_0_json": true, "mobility/tests/builders/test_json_version.py::TestJSONVersion::test_from_string_invalid_format": true, "mobility/tests/builders/test_json_version.py::TestJSONVersion::test_from_string_invalid_format[v1 ]": true, "mobility/tests/builders/test_json_version.py::TestJSONVersion::test_from_string_invalid_format[v 1]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\employee_id_to_coworking_site.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_modal_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_no_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\id_ref_poi_timed_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_bike_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_car_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute\\\\site_to_pt_stop_commute.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\base_commute_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\modal_commute_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\timed_commute_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\commute_data\\\\time_indexed_base_commute_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\crit_air\\\\crit_air_category.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\employees_mapping.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\failed_geo_employee.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\employee\\\\geo_employee.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\error\\\\error.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\address_details.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\geo_coordinates.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\multi_polygon.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\geo_data\\\\polygon.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\amenity_type.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\bike_amenity.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\bike_line.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\car_amenity.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\car_way.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\pt_line.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\infrastructure\\\\pt_stop.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\isochrone\\\\site_mode_isochrone_mapping.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\coworking_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_modal_commute_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_no_commute_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\id_ref_poi_commute_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_bike_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_car_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario\\\\site_to_pt_stop_scenario.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario_data\\\\scenario_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\scenario_data\\\\scenario_data_mapping.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\coworking_site.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\failed_geo_site.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\geo_site.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\site\\\\sites_mapping.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\study\\\\study_data.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\territory\\\\point_of_interest.json]": true, "mobility/tests/json_schemas/test_all_schemas.py::TestSchemas::test_all_schemas_have_correct_id[C:\\\\Users\\\\<USER>\\\\OneDrive - CITEC\\\\modelity\\\\mobility\\\\mobility\\\\json_schemas\\\\transport\\\\transport_type.json]": true, "mobility/tests/builders/test_json_converters.py::TestJsonConverters::test_all_converters_chain": true, "mobility/tests/ir/test_mode_constraints.py::TestDurationConstraints::test_from_dict_invalid_value_should_raise_error": true, "mobility/tests/workers/test_transport_mode_computer.py::TestRuleBasedTransportModeAssigner::test_eligible_for_soft_mobility_when_no_constraints": true, "mobility/tests/workers/test_transport_mode_computer.py::TestFormStatsModeAssigner::test_should_assign_limited_mode_depending_on_threshold[WALK-1500-BICYCLE]": true, "mobility/tests/builders/test_json_builder.py::TestJSONVersion::test_from_string_invalid_version_number_should_raise[v17]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_create_constraints_for_all_transport_modes": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_create_empty_constraints_when_no_duration_parameters": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_raise_error_when_duration_value_is_invalid": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_ignore_none_duration_values": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_create_proper_constraint_objects": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReaderExtractMaxDurations::test_extract_max_durations_should_handle_string_numeric_values": true, "mobility/tests/ir/test_mode_constraints.py::TestConstraints::test_from_dict_should_create_constraints": true, "mobility/tests/ir/test_mode_constraints.py::TestConstraints::test_invalid_duration_constraint_should_raise_error": true, "mobility/tests/ir/test_mode_constraints.py::TestConstraints::test_invalid_distance_constraint_should_raise_error": true, "mobility/tests/ir/test_mode_constraints.py::TestConstraints::test_invalid_format_duration_constraints_should_raise_error": true, "mobility/tests/ir/test_mode_constraints.py::TestConstraints::test_invalid_format_distance_constraints_should_raise_error": true, "mobility/tests/workers/test_transport_mode_computer.py::TestGetConstraints::test_should_return_fastest_constraints_for_fastest_mode_assigner": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_extract_max_durations_should_create_constraints_for_all_transport_modes": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_extract_max_durations_should_create_empty_constraints_when_no_duration_parameters": true, "api_abstraction/tests/here/test_here_api_clients.py::test_empty_routes_list_should_raise": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_extract_constraints_with_valid_max_transfers": true, "mobility/tests/builders/test_json_version_converters.py::TestJsonConverters::test_should_convert_json_v17_to_v18": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvParameterReader::test_extract_penalties_should_add_it_to_penalties": true, "api_abstraction/tests/here/test_here_api_clients.py::test_calculate_total_duration_from_timestamps_with_empty_sections_should_raise_error": true, "api_abstraction/tests/here/test_here_api_clients.py::test_result_with_invalid_structure_should_raise[result4]": true, "mobility/tests/builders/test_csv_multi_reader.py::TestCsvMultiReader::test_extract_site_parking_cost_should_parse_costs": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[invalid mode: 5.0-BICYCLE]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: invalid cost-BICYCLE]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: 5.0, voiture: invalid cost-CAR]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: 5.0, voiture: 10.0, moto: invalid cost-MOTORCYCLE]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: 5.0, voiture: 10.0, moto: 3.0]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: 2.5, voiture: 7.5]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_raise_error_on_invalid_format[v\\xe9lo: 0.0, voiture: 0.0]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_return_empty_dict_when_cost_column_is_not_found": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_parse_costs[voiture:10v\\xe9lo:5, voiture:10]": true, "mobility/tests/builders/test_csv_multi_reader.py::test_extract_site_parking_cost_should_parse_costs[voiture:10]": true, "mobility/tests/workers/test_transport_cost_calculator.py::TestActiveModeKmFees::test_should_compute_walk_km_fee": true, "mobility/tests/workers/test_transport_cost_calculator.py::TestActiveModeKmFees::test_should_compute_bicycle_km_fee": true, "mobility/tests/workers/test_transport_cost_calculator.py::TestPTSubscriptionCosts::test_should_compute_pt_subscription_cost_for_employee": true, "mobility/tests/workers/test_transport_cost_calculator.py::TestPTSubscriptionCosts::test_should_compute_pt_subscription_cost_for_company": true}