from mobility.quantity import euros, kilometers, yearly
from mobility.workers.work_accident_cost_calculator import WorkAccidentCostCalculator
import pytest


class TestWorkAccidentCostComputation:
    @pytest.fixture
    def calculator(self) -> WorkAccidentCostCalculator:
        return WorkAccidentCostCalculator()

    def test_should_compute_walk_work_accident_cost_for_society(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_walk_work_accident_cost_for_society(10 * kilometers)

        assert 940 * euros / yearly <= cost <= 941 * euros / yearly

    def test_should_compute_walk_work_accident_cost_for_company(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_walk_work_accident_cost_for_company(10 * kilometers)

        assert 103 * euros / yearly <= cost <= 104 * euros / yearly

    def test_should_compute_bicycle_work_accident_cost_for_society(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_bicycle_work_accident_cost_for_society(
            10 * kilometers
        )

        assert 1081 * euros / yearly <= cost <= 1082 * euros / yearly

    def test_should_compute_bicycle_work_accident_cost_for_company(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_bicycle_work_accident_cost_for_company(
            10 * kilometers
        )

        assert cost == 94 * euros / yearly

    def test_should_compute_pt_work_accident_cost_for_society(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_pt_work_accident_cost_for_society(10 * kilometers)

        assert 9 * euros / yearly <= cost <= 10 * euros / yearly

    def test_should_compute_pt_work_accident_cost_for_company(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_pt_work_accident_cost_for_company(10 * kilometers)

        assert 0 * euros / yearly <= cost <= 1 * euros / yearly

    def test_should_compute_car_work_accident_cost_for_society(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_car_work_accident_cost_for_society(10 * kilometers)

        assert 94 * euros / yearly <= cost <= 95 * euros / yearly

    def test_should_compute_car_work_accident_cost_for_company(
        self, calculator: WorkAccidentCostCalculator
    ) -> None:
        cost = calculator.compute_car_work_accident_cost_for_company(10 * kilometers)

        assert 9 * euros / yearly <= cost <= 10 * euros / yearly
