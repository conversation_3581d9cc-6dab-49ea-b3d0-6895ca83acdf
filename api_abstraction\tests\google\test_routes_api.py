from datetime import datetime
from typing import Any
from unittest.mock import Mock, patch

import google.api_core.exceptions as routes_api_exceptions
import pytest
from google.maps.routing_v2 import ComputeRoutesResponse, Route
from google.protobuf.duration_pb2 import Duration

from api_abstraction.api.api import ApiFail, ApiTimeout
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.google.base_google_api import GoogleRequestParameters
from api_abstraction.google.routes_api import RoutesAPI
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


@pytest.fixture
def fake_routes_exception_raiser():
    class fake_compute_route:
        def __init__(self, exception: Exception):
            self.e = exception

        def compute_routes(self, *args: Any, **kwargs: Any) -> None:
            raise self.e

    return fake_compute_route


@pytest.fixture(autouse=True)
def mock_routes_client() -> Any:
    with patch("api_abstraction.google.routes_api.RoutesClient") as mock:
        client = Mock()
        mock.from_service_account_file.return_value = client
        yield client


@pytest.fixture
def mock_route_response() -> ComputeRoutesResponse:
    route = Route()
    route.distance_meters = 55933
    route.duration = Duration(seconds=2763)
    response = ComputeRoutesResponse()
    response.routes.append(route)
    return response


class TestRoutesAPI:
    def test_compute_route_formats_request_and_returns_journey_attributes(
        self, mock_routes_client: Any, mock_route_response: ComputeRoutesResponse
    ) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        mock_routes_client.compute_routes.return_value = mock_route_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(46.1146, 4.7031),
            destination=GeoCoordinates(45.7668, 4.8566),
            mode=TransportMode.CAR,
            departure_time=datetime(2023, 1, 1, 8, 30),
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        result = api.compute_route(params)

        assert result == JourneyAttribute(duration=2763, distance=55933, emission=None)

    def test_compute_route_includes_field_mask_headers(
        self, mock_routes_client: Any, mock_route_response: ComputeRoutesResponse
    ) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        mock_routes_client.compute_routes.return_value = mock_route_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        with patch(
            "api_abstraction.google.routes_api.RoutesAPI._format_input_parameters"
        ) as mock_format_input_parameters:
            mock_format_input_parameters.return_value = {}
            api.compute_route(params)

            mock_routes_client.compute_routes.assert_called_once_with(
                {},
                metadata=[
                    ("x-goog-fieldmask", "routes.distanceMeters,routes.duration")
                ],
            )

    def test_raises_error_if_no_routes_returned(self, mock_routes_client: Any) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        empty_response = ComputeRoutesResponse()
        mock_routes_client.compute_routes.return_value = empty_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        with pytest.raises(ApiFail, match="Invalid response: wrong routes count 0"):
            api.compute_route(params)

    @pytest.mark.parametrize(
        "mode,expected_google_mode",
        [
            (TransportMode.WALK, "WALK"),
            (TransportMode.PUBLIC_TRANSPORT, "TRANSIT"),
            (TransportMode.CAR, "DRIVE"),
            (TransportMode.MOTORCYCLE, "TWO_WHEELER"),
            (TransportMode.ELECTRIC_CAR, "DRIVE"),
            (TransportMode.CARPOOLING, "DRIVE"),
            (TransportMode.BICYCLE, "BICYCLE"),
        ],
    )
    def test_maps_transport_modes_correctly(
        self, mode: TransportMode, expected_google_mode: str
    ) -> None:
        api = RoutesAPI("fake/path/credentials.json")

        result = api._map_google_mode(mode)

        assert result == expected_google_mode

    def test_raises_error_for_unsupported_mode(self) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        unsupported_mode = TransportMode.ELECTRIC_BICYCLE

        with pytest.raises(
            ApiFail, match=f"Mode {unsupported_mode.value} unavailable in mapping"
        ):
            api._map_google_mode(unsupported_mode)

    def test_formats_input_parameters_correctly(self) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        departure_time = datetime(2023, 1, 1, 8, 30)
        params = GoogleRequestParameters(
            origin=GeoCoordinates(46.1146, 4.7031),
            destination=GeoCoordinates(45.7668, 4.8566),
            mode=TransportMode.CAR,
            departure_time=departure_time,
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        result = api._format_input_parameters(params)

        assert result == {
            "origin": {
                "location": {"lat_lng": {"latitude": 46.1146, "longitude": 4.7031}}
            },
            "destination": {
                "location": {"lat_lng": {"latitude": 45.7668, "longitude": 4.8566}}
            },
            "travel_mode": "DRIVE",
            "departure_time": departure_time,
            "arrival_time": None,
            "language_code": "fr",
            "units": "METRIC",
            "routing_preference": "TRAFFIC_AWARE_OPTIMAL",
            "traffic_model": "PESSIMISTIC",
        }

    def test_skips_car_parameters_for_public_transport(self) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        departure_time = datetime(2023, 1, 1, 8, 30)
        params = GoogleRequestParameters(
            origin=GeoCoordinates(46.1146, 4.7031),
            destination=GeoCoordinates(45.7668, 4.8566),
            mode=TransportMode.PUBLIC_TRANSPORT,
            departure_time=departure_time,
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        result = api._format_input_parameters(params)

        assert "routing_preference" not in result
        assert "traffic_model" not in result
        assert result == {
            "origin": {
                "location": {"lat_lng": {"latitude": 46.1146, "longitude": 4.7031}}
            },
            "destination": {
                "location": {"lat_lng": {"latitude": 45.7668, "longitude": 4.8566}}
            },
            "travel_mode": "TRANSIT",
            "departure_time": departure_time,
            "arrival_time": None,
            "language_code": "fr",
            "units": "METRIC",
        }

    def test_extract_duration_and_distance(self) -> None:
        api = RoutesAPI("fake/path/credentials.json")
        route = Route()
        route.distance_meters = 55933
        route.duration = Duration(seconds=2763)

        duration = api._extract_duration_from_element(route)
        distance = api._extract_distance_from_element(route)

        assert duration == 2763
        assert distance == 55933

    def test_compute_route_fails_with_proper_exceptions(
        self, fake_routes_exception_raiser
    ):
        expected_exception_map = {
            routes_api_exceptions.DeadlineExceeded("timeout"): ApiTimeout,
            routes_api_exceptions.GoogleAPICallError("error"): ApiFail,
            routes_api_exceptions.RetryError("retry", None): ApiFail,
            routes_api_exceptions.ServiceUnavailable("unavailable"): ApiFail,
            routes_api_exceptions.TooManyRequests("too many"): ApiFail,
            routes_api_exceptions.Unknown("unknown"): ApiFail,
            routes_api_exceptions.BadRequest("bad request"): ApiFail,
        }
        google_apis = [
            (fake_routes_exception_raiser(ge), tte)
            for ge, tte in expected_exception_map.items()
        ]
        origin = GeoCoordinates(latitude=4.7031, longitude=46.1146)
        destination = GeoCoordinates(latitude=4.8566, longitude=45.7668)
        api = RoutesAPI("fake/path/credentials.json")
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
            routing_preference="TRAFFIC_AWARE_OPTIMAL",
            traffic_model="pessimistic",
        )

        for api_client, expected_exception in google_apis:
            api.google_routing_client = api_client
            with pytest.raises(expected_exception):
                api.compute_route(params)

    def test_deadline_exceeded_message_is_explicit(self, fake_routes_exception_raiser):
        api = RoutesAPI("fake/path/credentials.json")
        api.google_routing_client = fake_routes_exception_raiser(
            routes_api_exceptions.DeadlineExceeded("Badaboum")
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0.0, 1.0),
            destination=GeoCoordinates(1.0, 0.0),
            mode=TransportMode.WALK,
        )

        with pytest.raises(ApiTimeout, match="Badaboum"):
            api.compute_route(params)

    def test_api_error_message_is_explicit(self, fake_routes_exception_raiser):
        api = RoutesAPI("fake/path/credentials.json")
        api.google_routing_client = fake_routes_exception_raiser(
            routes_api_exceptions.GoogleAPICallError("API call failed")
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0.0, 1.0),
            destination=GeoCoordinates(1.0, 0.0),
            mode=TransportMode.WALK,
        )

        with pytest.raises(ApiFail, match="API call failed"):
            api.compute_route(params)

    def test_service_unavailable_message_is_explicit(
        self, fake_routes_exception_raiser
    ):
        api = RoutesAPI("fake/path/credentials.json")
        api.google_routing_client = fake_routes_exception_raiser(
            routes_api_exceptions.ServiceUnavailable("Service is unavailable")
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0.0, 1.0),
            destination=GeoCoordinates(1.0, 0.0),
            mode=TransportMode.WALK,
        )

        with pytest.raises(ApiFail, match="Service is unavailable"):
            api.compute_route(params)

    def test_too_many_requests_message_is_explicit(self, fake_routes_exception_raiser):
        api = RoutesAPI("fake/path/credentials.json")
        api.google_routing_client = fake_routes_exception_raiser(
            routes_api_exceptions.TooManyRequests("Too many requests")
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0.0, 1.0),
            destination=GeoCoordinates(1.0, 0.0),
            mode=TransportMode.WALK,
        )

        with pytest.raises(ApiFail, match="Too many requests"):
            api.compute_route(params)

    def test_unknown_error_message_is_explicit(self, fake_routes_exception_raiser):
        api = RoutesAPI("fake/path/credentials.json")
        api.google_routing_client = fake_routes_exception_raiser(
            routes_api_exceptions.Unknown("Unknown error occurred")
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0.0, 1.0),
            destination=GeoCoordinates(1.0, 0.0),
            mode=TransportMode.WALK,
        )

        with pytest.raises(ApiFail, match="Unknown error occurred"):
            api.compute_route(params)
