from typing import List

from mobility.ir.cost import Cost, Cost<PERSON>ind, CostPayer
from mobility.quantity import (
    Quantity,
    euros,
    euros_2019,
    yearly,
)

TURNOVER_BEFORE_MOBILITY_PLAN = 15 / yearly / 100
TURNOVER_AFTER_MOBILITY_PLAN = 13 / yearly / 100
MONTHLY_MEDIAN_SALARY = 3_180 * euros_2019
YEARLY_MEDIAN_SALARY = MONTHLY_MEDIAN_SALARY * 12
YEARLY_MEAN_SUPER_GROSS_SALARY = 51808 * euros_2019
EMPLOYEE_TURNOVER_COST = 6 * MONTHLY_MEDIAN_SALARY
ABSENTEEISM_RATE_BEFORE_MOBILITY_PLAN = 5 / yearly / 100
ABSENTEEISM_RATE_AFTER_MOBILITY_PLAN = 475 / yearly / 10000
ABSENTEEISM_COST = YEARLY_MEAN_SUPER_GROSS_SALARY * 2
SUSTAINABLE_MOBILITY_FEE = 300 * euros / yearly


class EmployeeCostCalculator:
    def compute_company_attractiveness_cost_per_employee(self) -> Quantity:
        return (
            -abs(TURNOVER_BEFORE_MOBILITY_PLAN - TURNOVER_AFTER_MOBILITY_PLAN)
            * 2
            * EMPLOYEE_TURNOVER_COST
        )

    def compute_absenteeism_cost_per_employee(self) -> Quantity:
        return (
            -abs(
                ABSENTEEISM_RATE_BEFORE_MOBILITY_PLAN - ABSENTEEISM_RATE_AFTER_MOBILITY_PLAN
            )
            * ABSENTEEISM_COST
        )

    def compute_sustainable_mobility_fee_for_employee(self) -> Quantity:
        return -SUSTAINABLE_MOBILITY_FEE

    def compute_sustainable_mobility_fee_for_company(self) -> Quantity:
        return SUSTAINABLE_MOBILITY_FEE

    def _compute_mobility_plan_benefits(self) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=self.compute_company_attractiveness_cost_per_employee(),
            ),
            Cost(
                kind=CostKind.ABSENTEEISM,
                payer=CostPayer.COMPANY,
                amount=self.compute_absenteeism_cost_per_employee(),
            ),
        ]

    def _compute_sustainability_fee(self) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=self.compute_sustainable_mobility_fee_for_employee(),
            ),
            Cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=self.compute_sustainable_mobility_fee_for_company(),
            ),
        ]
