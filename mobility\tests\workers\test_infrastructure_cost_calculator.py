from typing import Any

import pytest

from mobility.ir.cost import Cost, CostKind, CostPayer
from mobility.quantity import euros, trip, yearly
from mobility.workers.costs import BICYCLE_PARKING_YEARLY_COST, CAR_PARKING_COST
from mobility.workers.infrastructure_cost_calculator import InfrastructureCostCalculator


@pytest.fixture
def calculator() -> InfrastructureCostCalculator:
    return InfrastructureCostCalculator()


class TestCarParkingCostComputation:
    def test_compute_car_parking_infrastructure_cost(
        self, calculator: InfrastructureCostCalculator
    ) -> None:
        cost = calculator.compute_car_parking_infrastructure_cost()

        assert 760 * euros / yearly <= cost <= 761 * euros / yearly


class TestCarParkingCommuteCostComputation:
    def test_should_compute_car_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=500.0,
        )

        costs = calculator._compute_car_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_car_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=None,
        )
        costs = calculator._compute_car_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_car_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = coworking_site()
        costs = calculator._compute_car_parking_commute_costs(site)

        assert costs == []


class TestRentOfficeCostComputation:
    def test_compute_office_cost_when_office_in_paris(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        paris = geo_site_factory(address_details=address(citycode="75102"))
        cost = calculator.compute_office_rent_cost(paris)

        assert 13582 * euros / yearly < cost < 13583 * euros / yearly

    def test_compute_office_cost_when_office_in_drome(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        drome = geo_site_factory(address_details=address(citycode="26xxx"))
        cost = calculator.compute_office_rent_cost(drome)

        assert 3855 * euros / yearly < cost < 3856 * euros / yearly

    def test_compute_office_cost_when_office_in_somewhere_very_far_away(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        utopia_city = geo_site_factory(address_details=address(citycode="L@vE"))
        cost = calculator.compute_office_rent_cost(utopia_city)

        assert 2948 * euros / yearly < cost < 2949 * euros / yearly


class TestComputeBicycleParkingCost:
    def test_should_compute_parking_infrastructure_cost_for_bikes(
        self, calculator: InfrastructureCostCalculator
    ) -> None:
        cost = calculator.compute_bicycle_parking_infrastructure_cost()

        assert 126 * euros / yearly <= cost <= 127 * euros / yearly


class TestBicycleParkingCommuteCostComputation:
    def test_should_compute_bicycle_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=500.0,
        )
        costs = calculator._compute_bicycle_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_bicycle_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=None,
        )
        costs = calculator._compute_bicycle_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, calculator: InfrastructureCostCalculator
    ) -> None:
        site = coworking_site()
        costs = calculator._compute_bicycle_parking_commute_costs(site)

        assert costs == []
